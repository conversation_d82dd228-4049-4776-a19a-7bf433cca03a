# 新架构测试文件
"""
这个文件包含了对新架构各个组件的基础测试。
运行这个文件可以验证重构后的代码是否正常工作。
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.events import EventManager, EventType, GameEvent, EventListener
from core.components import ComponentManager, AttributeComponent, SkillComponent, BuffComponent
from core.base_role import BaseRole, RoleState
from core.effects import DamageEffect, HealEffect, AttributeChangeEffect
from core.role_factory import RoleRegistry, RoleFactory, role_decorator
from core.skill_system import SkillTemplate, ConfigurableSkill, TargetType
from core.buff_system import BuffTemplate, ConfigurableBuff, BuffEffectType, BuffTriggerType


class TestEventSystem(unittest.TestCase):
    """测试事件系统"""
    
    def setUp(self):
        self.event_manager = EventManager()
        self.event_manager.clear_all_listeners()
        self.events_received = []
    
    def test_event_registration_and_firing(self):
        """测试事件注册和触发"""
        
        class TestListener(EventListener):
            def __init__(self, test_case):
                self.test_case = test_case
            
            def get_priority(self):
                return 100
            
            def handle_event(self, event):
                self.test_case.events_received.append(event.event_type)
        
        listener = TestListener(self)
        self.event_manager.register_listener(EventType.TURN_START, listener)
        
        # 触发事件
        event = GameEvent(EventType.TURN_START)
        self.event_manager.fire_event(event)
        
        self.assertIn(EventType.TURN_START, self.events_received)
    
    def test_event_priority(self):
        """测试事件优先级"""
        execution_order = []
        
        class HighPriorityListener(EventListener):
            def get_priority(self):
                return 1
            
            def handle_event(self, event):
                execution_order.append("high")
        
        class LowPriorityListener(EventListener):
            def get_priority(self):
                return 100
            
            def handle_event(self, event):
                execution_order.append("low")
        
        high_listener = HighPriorityListener()
        low_listener = LowPriorityListener()
        
        self.event_manager.register_listener(EventType.TURN_START, low_listener)
        self.event_manager.register_listener(EventType.TURN_START, high_listener)
        
        event = GameEvent(EventType.TURN_START)
        self.event_manager.fire_event(event)
        
        self.assertEqual(execution_order, ["high", "low"])


class TestComponentSystem(unittest.TestCase):
    """测试组件系统"""
    
    def setUp(self):
        # 创建一个简单的角色用于测试
        class TestRole(BaseRole):
            def get_role_position(self):
                return "测试型"
            
            def init_role_data(self, role_data):
                pass
        
        self.role = TestRole(user_id=1, role_id=1, name="测试角色")
    
    def test_attribute_component(self):
        """测试属性组件"""
        attr = AttributeComponent("test_attr", "测试属性", 100)
        self.assertEqual(attr.get_final_value(), 100)
        
        # 添加修饰符
        attr.add_modifier("add", 50, "test_source")
        self.assertEqual(attr.get_final_value(), 150)
        
        attr.add_modifier("multiply", 1.5, "test_source2")
        self.assertEqual(attr.get_final_value(), 225)  # (100 + 50) * 1.5
        
        # 移除修饰符
        attr.remove_modifier("test_source")
        self.assertEqual(attr.get_final_value(), 150)  # 100 * 1.5
    
    def test_component_manager(self):
        """测试组件管理器"""
        manager = ComponentManager(self.role)
        
        # 添加组件
        attr = AttributeComponent("test_attr", "测试属性", 100)
        manager.add_component(attr)
        
        # 检查组件是否添加成功
        self.assertTrue(manager.has_component("test_attr"))
        self.assertEqual(manager.get_component("test_attr"), attr)
        
        # 移除组件
        manager.remove_component("test_attr")
        self.assertFalse(manager.has_component("test_attr"))


class TestEffectSystem(unittest.TestCase):
    """测试效果系统"""
    
    def setUp(self):
        class TestRole(BaseRole):
            def get_role_position(self):
                return "测试型"
            
            def init_role_data(self, role_data):
                pass
        
        self.caster = TestRole(user_id=1, role_id=1, name="施法者")
        self.target = TestRole(user_id=2, role_id=2, name="目标")
        
        # 设置基础属性
        self.caster.set_base_attribute('attack', 100)
        self.target.set_base_attribute('current_health', 500)
        self.target.set_base_attribute('max_health', 500)
    
    def test_damage_effect(self):
        """测试伤害效果"""
        damage_effect = DamageEffect(
            base_damage=50,
            scale_attr="attack",
            scale_ratio=1.0
        )
        
        old_health = self.target.get_attribute('current_health')
        result = damage_effect.apply(self.caster, self.target, {})
        new_health = self.target.get_attribute('current_health')
        
        # 验证伤害计算 (50 + 100 * 1.0 = 150)
        expected_damage = 150
        self.assertAlmostEqual(old_health - new_health, expected_damage, delta=10)
        self.assertIn('damage_dealt', result)
    
    def test_heal_effect(self):
        """测试治疗效果"""
        # 先让目标受伤
        self.target.change_attribute('current_health', -200)
        
        heal_effect = HealEffect(
            base_heal=100,
            scale_attr="attack",
            scale_ratio=0.5
        )
        
        old_health = self.target.get_attribute('current_health')
        result = heal_effect.apply(self.caster, self.target, {})
        new_health = self.target.get_attribute('current_health')
        
        # 验证治疗计算 (100 + 100 * 0.5 = 150)
        expected_heal = 150
        self.assertEqual(new_health - old_health, expected_heal)
        self.assertIn('heal_amount', result)
    
    def test_attribute_change_effect(self):
        """测试属性改变效果"""
        attr_effect = AttributeChangeEffect(
            attr_name="attack",
            base_change=50
        )
        
        old_attack = self.target.get_attribute('attack')
        result = attr_effect.apply(self.caster, self.target, {})
        new_attack = self.target.get_attribute('attack')
        
        self.assertEqual(new_attack - old_attack, 50)
        self.assertIn('change', result)


class TestRoleFactory(unittest.TestCase):
    """测试角色工厂"""
    
    def setUp(self):
        self.registry = RoleRegistry()
        self.factory = RoleFactory()
    
    def test_role_registration(self):
        """测试角色注册"""
        
        @role_decorator(
            role_id=9999,
            name="测试角色",
            position="测试型",
            health=1000
        )
        class TestRole(BaseRole):
            def get_role_position(self):
                return "测试型"
            
            def init_role_data(self, role_data):
                self.set_base_attribute('max_health', role_data.get('health', 1000))
        
        # 验证注册
        self.assertTrue(self.registry.is_role_registered(9999))
        config = self.registry.get_role_config(9999)
        self.assertEqual(config['name'], "测试角色")
    
    def test_role_creation(self):
        """测试角色创建"""
        
        @role_decorator(
            role_id=9998,
            name="创建测试角色",
            position="测试型",
            health=1200
        )
        class CreateTestRole(BaseRole):
            def get_role_position(self):
                return "测试型"
            
            def init_role_data(self, role_data):
                self.set_base_attribute('max_health', role_data.get('health', 1200))
        
        # 创建角色
        role = self.factory.create_role(9998, user_id=123)
        
        self.assertIsNotNone(role)
        self.assertEqual(role.user_id, 123)
        self.assertEqual(role.role_id, 9998)
        self.assertEqual(role.name, "创建测试角色")


class TestSkillSystem(unittest.TestCase):
    """测试技能系统"""
    
    def test_skill_template_creation(self):
        """测试技能模板创建"""
        template = SkillTemplate(
            template_id="test_skill",
            name="测试技能",
            description="这是一个测试技能",
            tp_cost=20,
            target_type=TargetType.SELECT,
            effects=[
                {
                    "type": "damage",
                    "base_damage": 100,
                    "scale_attr": "attack",
                    "scale_ratio": 1.0
                }
            ]
        )
        
        self.assertEqual(template.template_id, "test_skill")
        self.assertEqual(template.tp_cost, 20)
        self.assertEqual(template.target_type, TargetType.SELECT)
    
    def test_configurable_skill(self):
        """测试可配置技能"""
        template = SkillTemplate(
            template_id="test_skill",
            name="测试技能",
            description="这是一个测试技能",
            tp_cost=20,
            target_type=TargetType.SELF,
            effects=[]
        )
        
        skill = ConfigurableSkill(template)
        self.assertEqual(skill.skill_id, "test_skill")
        self.assertEqual(skill.tp_cost, 20)


class TestBuffSystem(unittest.TestCase):
    """测试Buff系统"""
    
    def test_buff_template_creation(self):
        """测试Buff模板创建"""
        template = BuffTemplate(
            template_id="test_buff",
            name="测试Buff",
            description="这是一个测试Buff",
            effect_type=BuffEffectType.ATTRIBUTE_MODIFIER,
            trigger_type=BuffTriggerType.IMMEDIATE,
            duration=3,
            effect_config={
                'attribute': 'attack',
                'modifier_type': 'add',
                'value': 50
            }
        )
        
        self.assertEqual(template.template_id, "test_buff")
        self.assertEqual(template.effect_type, BuffEffectType.ATTRIBUTE_MODIFIER)
        self.assertEqual(template.duration, 3)


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行新架构测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestEventSystem,
        TestComponentSystem,
        TestEffectSystem,
        TestRoleFactory,
        TestSkillSystem,
        TestBuffSystem
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！新架构工作正常。")
    else:
        print(f"\n❌ 测试失败！失败数量: {len(result.failures)}, 错误数量: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
