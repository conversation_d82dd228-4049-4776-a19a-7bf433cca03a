# 新架构使用示例
"""
这个文件展示了如何使用新的重构架构来创建和管理游戏。
"""

import asyncio
from typing import Dict, Any
from core.game_manager import NewPCRScrimmage
from core.role_factory import RoleRegistry, auto_register_roles_from_directory
from core.events import EventType, GameEvent, EventListener, register_listener


class GameEventLogger(EventListener):
    """游戏事件日志记录器"""
    
    def __init__(self):
        # 注册感兴趣的事件
        register_listener(EventType.GAME_START, self)
        register_listener(EventType.TURN_START, self)
        register_listener(EventType.SKILL_CAST, self)
        register_listener(EventType.PLAYER_DEATH, self)
        register_listener(EventType.GAME_END, self)
    
    def get_priority(self) -> int:
        return 1000  # 低优先级，最后处理
    
    def handle_event(self, event: GameEvent):
        """处理事件并记录日志"""
        if event.event_type == EventType.GAME_START:
            print("🎮 游戏开始！")
        
        elif event.event_type == EventType.TURN_START:
            player_name = getattr(event.source, 'name', 'Unknown')
            print(f"🎯 {player_name} 的回合开始")
        
        elif event.event_type == EventType.SKILL_CAST:
            caster_name = getattr(event.source, 'name', 'Unknown')
            skill_name = event.data.get('skill', {}).name if event.data.get('skill') else 'Unknown'
            target_name = getattr(event.target, 'name', 'Unknown') if event.target else 'None'
            print(f"⚡ {caster_name} 使用了 {skill_name}" + (f" 对 {target_name}" if event.target else ""))
        
        elif event.event_type == EventType.PLAYER_DEATH:
            player_name = getattr(event.source, 'name', 'Unknown')
            print(f"💀 {player_name} 出局了！")
        
        elif event.event_type == EventType.GAME_END:
            print("🏆 游戏结束！")
            rankings = event.data.get('rankings', {})
            if rankings:
                print("最终排名：")
                for rank in sorted(rankings.keys()):
                    user_id = rankings[rank]
                    print(f"  第{rank}名: 用户{user_id}")


async def example_game_flow():
    """示例游戏流程"""
    print("=== PCR大乱斗新架构示例 ===\n")
    
    # 1. 自动注册角色
    print("📝 注册角色...")
    auto_register_roles_from_directory("roles")
    
    registry = RoleRegistry()
    available_roles = registry.get_all_role_ids()
    print(f"可用角色: {available_roles}")
    
    # 2. 创建游戏事件日志记录器
    logger = GameEventLogger()
    
    # 3. 创建游戏房间
    print("\n🏠 创建游戏房间...")
    game = NewPCRScrimmage(group_id=12345, room_master=1001)
    
    # 4. 玩家加入房间
    print("\n👥 玩家加入房间...")
    players = [1001, 1002, 1003, 1004]
    for player_id in players:
        success = game.join_room(player_id)
        print(f"玩家 {player_id} 加入: {'成功' if success else '失败'}")
    
    # 5. 玩家选择角色
    print("\n🎭 玩家选择角色...")
    role_assignments = {
        1001: 1701,  # 环奈
        1002: 5105,  # 嘉然
        1003: 1701,  # 环奈（如果有的话）
        1004: 5105,  # 嘉然（如果有的话）
    }
    
    for player_id, role_id in role_assignments.items():
        if role_id in available_roles:
            success = game.select_role(player_id, role_id)
            role_name = registry.get_role_config(role_id).get('name', f'Role_{role_id}') if registry.get_role_config(role_id) else f'Role_{role_id}'
            print(f"玩家 {player_id} 选择角色 {role_name}: {'成功' if success else '失败'}")
        else:
            # 选择第一个可用角色
            if available_roles:
                role_id = available_roles[0]
                success = game.select_role(player_id, role_id)
                role_name = registry.get_role_config(role_id).get('name', f'Role_{role_id}') if registry.get_role_config(role_id) else f'Role_{role_id}'
                print(f"玩家 {player_id} 选择角色 {role_name}: {'成功' if success else '失败'}")
    
    # 6. 开始游戏
    print("\n🚀 开始游戏...")
    success = game.start_game()
    print(f"游戏开始: {'成功' if success else '失败'}")
    
    if not success:
        print("游戏开始失败，退出示例")
        return
    
    # 7. 模拟游戏回合
    print("\n🎲 模拟游戏回合...")
    max_rounds = 10  # 最大回合数，避免无限循环
    round_count = 0
    
    while game.state == "playing" and round_count < max_rounds:
        round_count += 1
        print(f"\n--- 第 {round_count} 轮 ---")
        
        current_player_id = game.current_turn_player_id
        if not current_player_id:
            break
        
        current_player = game.players[current_player_id]
        print(f"当前玩家: {current_player.name} (ID: {current_player_id})")
        
        # 投掷骰子
        if current_player.state.value == "dice_phase":
            steps = game.throw_dice(current_player_id)
            if steps:
                print(f"🎲 投掷骰子: {steps} 步")
                print(f"📍 新位置: {current_player.position}")
        
        # 使用技能或跳过
        if current_player.state.value == "skill_phase":
            skills = current_player.get_skills()
            if skills and len(skills) > 0:
                # 随机选择一个技能
                import random
                skill_index = random.randint(0, len(skills) - 1)
                
                # 随机选择目标（如果需要）
                other_players = [p for p in game.players.values() if p != current_player and p.is_alive()]
                target_id = None
                if other_players:
                    target = random.choice(other_players)
                    target_id = target.user_id
                
                success = game.use_skill(current_player_id, skill_index, target_id)
                if success:
                    skill_name = skills[skill_index].name
                    target_name = game.players[target_id].name if target_id else "无"
                    print(f"⚡ 使用技能: {skill_name}, 目标: {target_name}")
                else:
                    # 技能使用失败，跳过
                    game.skip_skill(current_player_id)
                    print("⏭️ 跳过技能阶段")
            else:
                # 没有技能，跳过
                game.skip_skill(current_player_id)
                print("⏭️ 跳过技能阶段")
        
        # 显示当前游戏状态
        alive_players = [p for p in game.players.values() if p.is_alive()]
        print(f"存活玩家数: {len(alive_players)}")
        
        # 短暂延迟，模拟真实游戏节奏
        await asyncio.sleep(0.1)
    
    # 8. 显示游戏结果
    print(f"\n🏁 游戏结束！状态: {game.state}")
    if game.rankings:
        print("🏆 最终排名:")
        for rank in sorted(game.rankings.keys()):
            user_id = game.rankings[rank]
            player = game.players.get(user_id)
            player_name = player.name if player else f"玩家{user_id}"
            print(f"  第 {rank} 名: {player_name}")
    
    # 9. 显示详细的玩家信息
    print("\n📊 玩家详细信息:")
    for user_id, player in game.players.items():
        if player:
            info = game.get_player_info(user_id)
            print(f"\n玩家: {info['name']} (ID: {user_id})")
            print(f"  状态: {info['state']}")
            print(f"  位置: {info['position']}")
            print(f"  属性: {info['attributes']}")
            print(f"  技能数量: {len(info['skills'])}")
            print(f"  Buff数量: {len(info['buffs'])}")


def example_create_custom_role():
    """示例：创建自定义角色"""
    print("\n=== 创建自定义角色示例 ===")
    
    from core.base_role import BaseRole
    from core.components import SkillComponent
    from core.effects import DamageEffect
    from core.role_factory import role_decorator
    
    # 自定义技能组件
    class CustomSkill(SkillComponent):
        def __init__(self):
            super().__init__(
                skill_id="custom_skill",
                name="自定义技能",
                description="这是一个自定义技能",
                tp_cost=20
            )
            self.damage_effect = DamageEffect(base_damage=100, scale_attr="attack", scale_ratio=1.5)
        
        def can_cast(self, target=None) -> bool:
            return (self.owner and target and 
                   self.owner.get_attribute('current_tp') >= self.tp_cost)
        
        def cast(self, target=None) -> bool:
            if not self.can_cast(target):
                return False
            
            self.owner.change_attribute('current_tp', -self.tp_cost)
            context = {'skill': self, 'caster': self.owner}
            self.damage_effect.apply(self.owner, target, context)
            return True
    
    # 自定义角色类
    @role_decorator(
        role_id=9999,
        name="自定义角色",
        position="测试型",
        health=1500,
        attack=120,
        defense=60,
        distance=8,
        tp=0,
        crit=15
    )
    class CustomRole(BaseRole):
        def get_role_position(self) -> str:
            return "测试型"
        
        def init_role_data(self, role_data: Dict[str, Any]):
            # 设置基础属性
            for attr_name, value in [
                ('max_health', role_data.get('health', 1500)),
                ('current_health', role_data.get('health', 1500)),
                ('attack', role_data.get('attack', 120)),
                ('defense', role_data.get('defense', 60)),
                ('distance', role_data.get('distance', 8)),
                ('current_tp', role_data.get('tp', 0)),
                ('crit_rate', role_data.get('crit', 15))
            ]:
                self.set_base_attribute(attr_name, value)
            
            # 添加自定义技能
            self.add_component(CustomSkill())
    
    print("✅ 自定义角色创建完成！")
    print(f"角色ID: 9999")
    print(f"角色名: 自定义角色")
    
    # 验证角色是否注册成功
    registry = RoleRegistry()
    if registry.is_role_registered(9999):
        print("✅ 角色注册成功！")
        config = registry.get_role_config(9999)
        print(f"角色配置: {config}")
    else:
        print("❌ 角色注册失败！")


async def main():
    """主函数"""
    try:
        # 运行游戏流程示例
        await example_game_flow()
        
        # 创建自定义角色示例
        example_create_custom_role()
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
