# PCR大乱斗重构迁移指南

## 概述

这次重构将原有的硬编码角色系统重构为基于组件的可扩展架构。新架构的主要优势：

1. **组件化设计**：角色能力被拆分为独立的组件
2. **事件驱动**：通过事件系统解耦各模块
3. **配置化**：技能和Buff可通过配置文件定义
4. **可扩展性**：添加新角色无需修改核心代码
5. **维护性**：代码结构清晰，易于理解和维护

## 新架构结构

```
core/
├── events.py          # 事件系统
├── components.py      # 组件系统基础类
├── base_role.py       # 基础角色类
├── effects.py         # 技能效果系统
├── skill_system.py    # 技能系统
├── buff_system.py     # Buff系统
├── role_factory.py    # 角色工厂和注册系统
└── game_manager.py    # 新的游戏管理器

roles/
├── kanna.py          # 环奈角色实现示例
└── diana.py          # 嘉然角色实现示例

config/
├── skills.json       # 技能配置文件
└── buffs.json        # Buff配置文件
```

## 迁移步骤

### 1. 创建新角色类

旧的角色定义方式：
```python
# 在role.py中硬编码
ROLE = {
    1701: {
        "name": "环奈",
        "health": 1000,
        "attack": 110,
        # ... 其他属性和技能
    }
}
```

新的角色定义方式：
```python
# roles/kanna.py
from core.base_role import BaseRole
from core.role_factory import role_decorator

@role_decorator(
    role_id=1701,
    name="环奈",
    position="输出型",
    health=1000,
    attack=110,
    # ... 其他属性
)
class KannaRole(BaseRole):
    def get_role_position(self) -> str:
        return "输出型"
    
    def init_role_data(self, role_data: Dict[str, Any]):
        # 设置基础属性
        self.set_base_attribute('max_health', role_data.get('health', 1000))
        # ... 其他属性设置
        
        # 添加技能组件
        self.add_component(KannaNormalAttack())
        self.add_component(KannaShockBlade())
```

### 2. 创建技能组件

旧的技能定义方式：
```python
# 在role.py中硬编码技能效果
"active_skills": [
    {
        "name": "普通攻击",
        "tp_cost": 0,
        "effect": {
            EFFECT_HURT: (0, Attr.ATTACK, 0, 1, False)
        }
    }
]
```

新的技能定义方式：
```python
# 作为独立的组件类
class KannaNormalAttack(SkillComponent):
    def __init__(self):
        super().__init__(
            skill_id="kanna_normal_attack",
            name="普通攻击",
            description="对目标造成伤害",
            tp_cost=0
        )
        self.damage_effect = DamageEffect(
            base_damage=0,
            scale_attr="attack",
            scale_ratio=1.0
        )
    
    def can_cast(self, target=None) -> bool:
        # 检查释放条件
        return True
    
    def cast(self, target=None) -> bool:
        # 执行技能效果
        result = self.damage_effect.apply(self.owner, target, context)
        return True
```

### 3. 处理特殊机制

对于复杂的角色机制（如嘉然的嘉心糖），创建专门的被动组件：

```python
class SweetiePassive(PassiveComponent):
    def __init__(self):
        super().__init__(
            passive_id="diana_sweetie_passive",
            name="嘉心糖支持",
            description="嘉然拥有嘉心糖的支持"
        )
        self.sweetie_count = 3
    
    def handle_event(self, event: GameEvent):
        # 处理相关事件
        if event.event_type == EventType.ATTR_CHANGED:
            self._check_sweetie_loss()
```

### 4. 使用配置文件定义通用技能

对于通用技能，可以使用配置文件定义：

```json
{
  "id": "normal_attack",
  "name": "普通攻击",
  "tp_cost": 0,
  "target_type": "select_except_self",
  "effects": [
    {
      "type": "damage",
      "base_damage": 0,
      "scale_attr": "attack",
      "scale_ratio": 1.0
    }
  ]
}
```

然后在角色初始化时加载：
```python
def init_role_data(self, role_data: Dict[str, Any]):
    # 从配置创建技能
    normal_attack = create_skill("normal_attack")
    if normal_attack:
        self.add_component(normal_attack)
```

## 主要变化对比

### 旧架构问题
1. **硬编码逻辑**：所有角色特殊逻辑都写在主类中
2. **if语句泛滥**：每个角色都需要大量的条件判断
3. **难以扩展**：添加新角色需要修改多个文件
4. **代码重复**：相似功能在多处重复实现

### 新架构优势
1. **组件化**：每个能力都是独立的组件
2. **事件驱动**：通过事件系统实现解耦
3. **配置化**：通过配置文件定义技能和Buff
4. **可扩展**：添加新角色只需创建新的角色类文件

## 迁移检查清单

- [ ] 创建新的角色类文件
- [ ] 实现角色的基础属性设置
- [ ] 将技能转换为技能组件
- [ ] 处理特殊被动技能
- [ ] 创建必要的Buff组件
- [ ] 测试角色功能完整性
- [ ] 更新角色注册
- [ ] 验证事件处理正确性

## 使用新架构的好处

1. **添加新角色**：只需创建一个新的角色文件，无需修改核心代码
2. **修改技能**：可以通过修改配置文件或组件类来调整技能
3. **调试方便**：每个组件都是独立的，便于调试和测试
4. **代码复用**：通用的技能效果可以在多个角色间复用
5. **维护简单**：代码结构清晰，职责分明

## 示例：完整的角色迁移

参考 `roles/kanna.py` 和 `roles/diana.py` 文件，这些文件展示了如何将复杂的角色逻辑迁移到新架构中。

## 注意事项

1. **事件监听器**：确保正确注册和注销事件监听器，避免内存泄漏
2. **组件生命周期**：正确处理组件的添加和移除
3. **属性修饰符**：使用属性修饰符系统而不是直接修改属性值
4. **配置验证**：确保配置文件的格式正确
5. **向后兼容**：在迁移过程中保持API的向后兼容性

通过这次重构，代码将变得更加模块化、可维护和可扩展。新的架构为未来的功能扩展奠定了坚实的基础。
