{"buffs": [{"id": "attack_boost", "name": "攻击力提升", "description": "增加攻击力", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 3, "stack_limit": 5, "effect_config": {"attribute": "attack", "modifier_type": "add", "value": 50}}, {"id": "defense_boost", "name": "防御力提升", "description": "增加防御力", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 3, "stack_limit": 5, "effect_config": {"attribute": "defense", "modifier_type": "add", "value": 30}}, {"id": "speed_boost", "name": "速度提升", "description": "增加攻击距离", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 4, "stack_limit": 3, "effect_config": {"attribute": "distance", "modifier_type": "add", "value": 2}}, {"id": "crit_boost", "name": "暴击提升", "description": "增加暴击率", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 5, "stack_limit": 10, "effect_config": {"attribute": "crit_rate", "modifier_type": "add", "value": 10}}, {"id": "regeneration", "name": "生命恢复", "description": "每回合恢复生命值", "effect_type": "heal_over_time", "trigger_type": "turn_end", "duration": 5, "stack_limit": 3, "effect_config": {"value": 100}}, {"id": "poison", "name": "中毒", "description": "每回合受到伤害", "effect_type": "damage_over_time", "trigger_type": "turn_end", "duration": 3, "stack_limit": 5, "effect_config": {"value": 50}}, {"id": "damage_shield", "name": "伤害护盾", "description": "吸收一定量的伤害", "effect_type": "damage_shield", "trigger_type": "before_hurt", "duration": 5, "stack_limit": 1, "effect_config": {"value": 300}}, {"id": "berserker_rage", "name": "狂暴状态", "description": "攻击力大幅提升但防御力降低", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 4, "stack_limit": 1, "effect_config": {"attribute": "attack", "modifier_type": "multiply", "value": 1.5}}, {"id": "weakness", "name": "虚弱", "description": "攻击力降低", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 3, "stack_limit": 3, "effect_config": {"attribute": "attack", "modifier_type": "multiply", "value": 0.8}}, {"id": "armor_break", "name": "破甲", "description": "防御力大幅降低", "effect_type": "attribute_modifier", "trigger_type": "immediate", "duration": 4, "stack_limit": 5, "effect_config": {"attribute": "defense", "modifier_type": "add", "value": -20}}, {"id": "diana_streaming", "name": "然然直播中", "description": "嘉然开始直播了！接下来每个自我回合获得嘉心糖", "effect_type": "custom", "trigger_type": "turn_end", "duration": 2, "stack_limit": 1, "effect_config": {"sweetie_gain": 2}}, {"id": "cat_poison", "name": "猫中毒", "description": "受到攻击时嘉然会获得额外收益", "effect_type": "custom", "trigger_type": "after_hurt", "duration": 3, "stack_limit": 1, "effect_config": {"diana_bonus": true}}, {"id": "invulnerable", "name": "无敌", "description": "免疫所有伤害", "effect_type": "immunity", "trigger_type": "before_hurt", "duration": 2, "stack_limit": 1, "effect_config": {"immunity_type": "damage"}}, {"id": "tp_boost", "name": "能量充盈", "description": "每回合额外获得TP", "effect_type": "custom", "trigger_type": "turn_end", "duration": 5, "stack_limit": 3, "effect_config": {"tp_gain": 15}}, {"id": "reflect_damage", "name": "伤害反射", "description": "将受到的伤害反射给攻击者", "effect_type": "damage_reflection", "trigger_type": "after_hurt", "duration": 3, "stack_limit": 1, "effect_config": {"reflect_ratio": 0.5}}]}