{"skills": [{"id": "normal_attack", "name": "普通攻击", "description": "对目标造成基础伤害", "tp_cost": 0, "target_type": "select_except_self", "distance_check": true, "effects": [{"type": "damage", "base_damage": 0, "scale_attr": "attack", "scale_ratio": 1.0, "is_true_damage": false, "crit_enabled": true}]}, {"id": "heal_self", "name": "自我治疗", "description": "恢复自身生命值", "tp_cost": 20, "target_type": "self", "distance_check": false, "effects": [{"type": "heal", "base_heal": 200, "scale_attr": "attack", "scale_ratio": 0.5}]}, {"id": "power_strike", "name": "强力打击", "description": "对目标造成高额伤害并降低其防御力", "tp_cost": 30, "target_type": "select_except_self", "distance_check": true, "effects": [{"type": "damage", "base_damage": 150, "scale_attr": "attack", "scale_ratio": 1.5, "is_true_damage": false, "crit_enabled": true}, {"type": "attr_change", "attr_name": "defense", "base_change": -50, "scale_attr": "", "scale_ratio": 0}]}, {"id": "aoe_blast", "name": "范围爆炸", "description": "对目标及其周围敌人造成伤害", "tp_cost": 40, "target_type": "select_except_self", "distance_check": true, "effects": [{"type": "aoe", "radius": 2, "include_self": false, "effects": [{"type": "damage", "base_damage": 120, "scale_attr": "attack", "scale_ratio": 1.2, "is_true_damage": false, "crit_enabled": true}]}]}, {"id": "team_buff", "name": "团队增益", "description": "为所有队友提供攻击力加成", "tp_cost": 50, "target_type": "all_except_self", "distance_check": false, "effects": [{"type": "buff", "buff_template": "attack_boost", "duration": 3, "stacks": 1}]}, {"id": "teleport_strike", "name": "瞬移打击", "description": "瞬移到目标身边并发动攻击", "tp_cost": 35, "target_type": "select_except_self", "distance_check": false, "effects": [{"type": "move", "steps": 0, "relative": false, "target_adjacent": true}, {"type": "damage", "base_damage": 100, "scale_attr": "attack", "scale_ratio": 1.3, "is_true_damage": false, "crit_enabled": true}]}, {"id": "defensive_stance", "name": "防御姿态", "description": "提升自身防御力并获得伤害护盾", "tp_cost": 25, "target_type": "self", "distance_check": false, "effects": [{"type": "attr_change", "attr_name": "defense", "base_change": 100, "scale_attr": "", "scale_ratio": 0}, {"type": "buff", "buff_template": "damage_shield", "duration": 5, "stacks": 1, "shield_value": 300}]}, {"id": "berserker_rage", "name": "狂暴", "description": "大幅提升攻击力但降低防御力", "tp_cost": 40, "target_type": "self", "distance_check": false, "effects": [{"type": "attr_change", "attr_name": "attack", "base_change": 200, "scale_attr": "", "scale_ratio": 0}, {"type": "attr_change", "attr_name": "defense", "base_change": -50, "scale_attr": "", "scale_ratio": 0}, {"type": "buff", "buff_template": "berserker_rage", "duration": 4, "stacks": 1}]}, {"id": "poison_dart", "name": "毒镖", "description": "对目标造成伤害并施加中毒效果", "tp_cost": 20, "target_type": "select_except_self", "distance_check": true, "effects": [{"type": "damage", "base_damage": 80, "scale_attr": "attack", "scale_ratio": 0.8, "is_true_damage": false, "crit_enabled": true}, {"type": "buff", "buff_template": "poison", "duration": 3, "stacks": 1}]}, {"id": "ultimate_blast", "name": "终极爆发", "description": "消耗大量TP造成巨额真实伤害", "tp_cost": 100, "target_type": "select_except_self", "distance_check": false, "cooldown": 5, "max_uses": 1, "effects": [{"type": "damage", "base_damage": 500, "scale_attr": "attack", "scale_ratio": 2.0, "is_true_damage": true, "crit_enabled": false}]}]}