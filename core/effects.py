# 技能效果系统 - 将技能效果模块化
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from enum import Enum
import random
import math
from .events import EventType, GameEvent, fire_event

if TYPE_CHECKING:
    from .base_role import BaseRole


class EffectType(Enum):
    """效果类型枚举"""
    DAMAGE = "damage"
    HEAL = "heal"
    BUFF = "buff"
    DEBUFF = "debuff"
    MOVE = "move"
    TELEPORT = "teleport"
    AOE = "aoe"
    ATTRIBUTE_CHANGE = "attribute_change"
    SPECIAL = "special"


class SkillEffect(ABC):
    """技能效果基类"""
    
    def __init__(self, effect_id: str, name: str, description: str = ""):
        self.effect_id = effect_id
        self.name = name
        self.description = description
    
    @abstractmethod
    def get_effect_type(self) -> EffectType:
        """获取效果类型"""
        pass
    
    @abstractmethod
    def apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> Dict[str, Any]:
        """应用效果"""
        pass
    
    def can_apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> bool:
        """检查是否可以应用效果"""
        return True


class DamageEffect(SkillEffect):
    """伤害效果"""
    
    def __init__(self, base_damage: float, scale_attr: str = "", scale_ratio: float = 1.0, 
                 is_true_damage: bool = False, crit_enabled: bool = True):
        super().__init__("damage", "伤害", "造成伤害")
        self.base_damage = base_damage
        self.scale_attr = scale_attr  # 缩放属性
        self.scale_ratio = scale_ratio  # 缩放比例
        self.is_true_damage = is_true_damage  # 是否为真实伤害
        self.crit_enabled = crit_enabled  # 是否可以暴击
    
    def get_effect_type(self) -> EffectType:
        return EffectType.DAMAGE
    
    def apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> Dict[str, Any]:
        """应用伤害效果"""
        # 计算基础伤害
        damage = self.base_damage
        
        # 添加属性缩放
        if self.scale_attr:
            scale_value = caster.get_attribute(self.scale_attr)
            damage += scale_value * self.scale_ratio
        
        # 检查暴击
        is_crit = False
        if self.crit_enabled:
            crit_rate = caster.get_attribute('crit_rate')
            if random.random() * 100 < crit_rate:
                is_crit = True
                crit_damage = caster.get_attribute('crit_damage')
                damage *= crit_damage
        
        # 触发受伤前事件
        if not self.is_true_damage:
            hurt_event = fire_event(EventType.BEFORE_HURT, 
                                   source=caster, 
                                   target=target, 
                                   data={'damage': damage, 'is_crit': is_crit})
            damage = hurt_event.data['damage']
        
        # 应用伤害
        target.change_attribute('current_health', -damage)
        
        # 触发受伤后事件
        fire_event(EventType.AFTER_HURT, 
                  source=caster, 
                  target=target, 
                  data={'damage': damage, 'is_crit': is_crit})
        
        return {'damage_dealt': damage, 'is_crit': is_crit}


class HealEffect(SkillEffect):
    """治疗效果"""
    
    def __init__(self, base_heal: float, scale_attr: str = "", scale_ratio: float = 1.0):
        super().__init__("heal", "治疗", "恢复生命值")
        self.base_heal = base_heal
        self.scale_attr = scale_attr
        self.scale_ratio = scale_ratio
    
    def get_effect_type(self) -> EffectType:
        return EffectType.HEAL
    
    def apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> Dict[str, Any]:
        """应用治疗效果"""
        heal = self.base_heal
        
        if self.scale_attr:
            scale_value = caster.get_attribute(self.scale_attr)
            heal += scale_value * self.scale_ratio
        
        # 触发治疗前事件
        heal_event = fire_event(EventType.BEFORE_HEAL, 
                               source=caster, 
                               target=target, 
                               data={'heal': heal})
        heal = heal_event.data['heal']
        
        # 应用治疗
        target.change_attribute('current_health', heal)
        
        # 触发治疗后事件
        fire_event(EventType.AFTER_HEAL, 
                  source=caster, 
                  target=target, 
                  data={'heal': heal})
        
        return {'heal_amount': heal}


class AttributeChangeEffect(SkillEffect):
    """属性改变效果"""
    
    def __init__(self, attr_name: str, base_change: float, scale_attr: str = "", scale_ratio: float = 0):
        super().__init__("attr_change", "属性改变", f"改变{attr_name}")
        self.attr_name = attr_name
        self.base_change = base_change
        self.scale_attr = scale_attr
        self.scale_ratio = scale_ratio
    
    def get_effect_type(self) -> EffectType:
        return EffectType.ATTRIBUTE_CHANGE
    
    def apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> Dict[str, Any]:
        """应用属性改变效果"""
        change = self.base_change
        
        if self.scale_attr and self.scale_ratio != 0:
            scale_value = target.get_attribute(self.scale_attr)
            change += scale_value * self.scale_ratio
        
        old_value = target.get_attribute(self.attr_name)
        target.change_attribute(self.attr_name, change)
        new_value = target.get_attribute(self.attr_name)
        
        return {'attribute': self.attr_name, 'old_value': old_value, 'new_value': new_value, 'change': change}


class MoveEffect(SkillEffect):
    """移动效果"""
    
    def __init__(self, steps: int, relative: bool = True):
        super().__init__("move", "移动", "改变位置")
        self.steps = steps
        self.relative = relative  # True为相对移动，False为绝对位置
    
    def get_effect_type(self) -> EffectType:
        return EffectType.MOVE
    
    def apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> Dict[str, Any]:
        """应用移动效果"""
        runway = context.get('runway', [])
        if not runway:
            return {'moved': False}
        
        old_position = target.position
        
        if self.relative:
            target.move_relative(self.steps, runway)
        else:
            target.move_to_position(self.steps, runway)
        
        new_position = target.position
        
        return {'old_position': old_position, 'new_position': new_position, 'moved': True}


class AOEEffect(SkillEffect):
    """范围效果"""
    
    def __init__(self, radius: int, include_self: bool = False, effects: List[SkillEffect] = None):
        super().__init__("aoe", "范围效果", f"半径{radius}的范围效果")
        self.radius = radius
        self.include_self = include_self
        self.effects = effects or []
    
    def get_effect_type(self) -> EffectType:
        return EffectType.AOE
    
    def apply(self, caster: 'BaseRole', target: 'BaseRole', context: Dict[str, Any]) -> Dict[str, Any]:
        """应用范围效果"""
        runway = context.get('runway', [])
        all_players = context.get('all_players', [])
        
        if not runway or not all_players:
            return {'targets_affected': 0}
        
        affected_targets = []
        center_position = target.position
        
        # 找到范围内的所有目标
        for i in range(center_position - self.radius, center_position + self.radius + 1):
            position = i
            # 处理环形跑道
            if position >= len(runway):
                position -= len(runway)
            elif position < 0:
                position += len(runway)
            
            if position < len(runway) and runway[position]["players"]:
                for player_id in runway[position]["players"]:
                    player = next((p for p in all_players if p.user_id == player_id), None)
                    if player and (self.include_self or player != caster):
                        affected_targets.append(player)
        
        # 对每个目标应用效果
        results = []
        for affected_target in affected_targets:
            for effect in self.effects:
                result = effect.apply(caster, affected_target, context)
                results.append(result)
        
        return {'targets_affected': len(affected_targets), 'results': results}


class EffectFactory:
    """效果工厂"""
    
    _effect_classes = {
        'damage': DamageEffect,
        'heal': HealEffect,
        'attr_change': AttributeChangeEffect,
        'move': MoveEffect,
        'aoe': AOEEffect,
    }
    
    @classmethod
    def create_effect(cls, effect_type: str, **kwargs) -> Optional[SkillEffect]:
        """创建效果"""
        effect_class = cls._effect_classes.get(effect_type)
        if effect_class:
            return effect_class(**kwargs)
        return None
    
    @classmethod
    def register_effect(cls, effect_type: str, effect_class: type):
        """注册新的效果类型"""
        cls._effect_classes[effect_type] = effect_class
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> Optional[SkillEffect]:
        """从配置创建效果"""
        effect_type = config.get('type')
        if not effect_type:
            return None
        
        # 移除type字段，其余作为参数
        kwargs = {k: v for k, v in config.items() if k != 'type'}
        return cls.create_effect(effect_type, **kwargs)
