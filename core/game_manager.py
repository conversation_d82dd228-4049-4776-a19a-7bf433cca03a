# 新的游戏管理器 - 整合新架构
from typing import Dict, List, Optional, Any
import random
import asyncio
from .base_role import BaseRole, RoleState
from .role_factory import RoleFactory, RoleRegistry
from .events import EventManager, EventType, GameEvent, fire_event
from .skill_system import load_skills_from_config
from .buff_system import load_buffs_from_config


class GameState:
    """游戏状态枚举"""
    WAITING = "waiting"
    ROLE_SELECTION = "role_selection"
    PLAYING = "playing"
    ENDED = "ended"
    WON = "won"


class NewPCRScrimmage:
    """新的PCR大乱斗游戏类"""
    
    def __init__(self, group_id: int, room_master: int, runway_size: int = 36):
        self.group_id = group_id
        self.room_master = room_master
        self.state = GameState.WAITING
        
        # 玩家和角色管理
        self.players: Dict[int, BaseRole] = {}  # user_id -> role
        self.max_players = 4
        self.current_turn_player_id = None
        self.turn_order: List[int] = []
        
        # 游戏状态
        self.round_count = 0
        self.dice_count = 0
        self.rankings: Dict[int, int] = {}  # rank -> user_id
        
        # 跑道系统
        self.runway_size = runway_size
        self.runway = [{"players": [], "case": random.randint(0, 5)} for _ in range(runway_size)]
        
        # 工厂和管理器
        self.role_factory = RoleFactory()
        self.event_manager = EventManager()
        
        # 加载配置
        self._load_configurations()
        
        # 用户信息缓存
        self.user_card_dict = {}
    
    def _load_configurations(self):
        """加载配置文件"""
        try:
            load_skills_from_config('config/skills.json')
            load_buffs_from_config('config/buffs.json')
        except Exception as e:
            print(f"Failed to load configurations: {e}")
    
    # 房间管理
    def can_join(self, user_id: int) -> bool:
        """检查用户是否可以加入"""
        return (len(self.players) < self.max_players and 
                user_id not in self.players and
                self.state == GameState.WAITING)
    
    def join_room(self, user_id: int) -> bool:
        """加入房间"""
        if not self.can_join(user_id):
            return False
        
        # 创建一个临时角色占位
        self.players[user_id] = None
        return True
    
    def leave_room(self, user_id: int) -> bool:
        """离开房间"""
        if user_id not in self.players:
            return False
        
        if self.players[user_id]:
            # 清理角色的事件监听器
            role = self.players[user_id]
            # 这里应该清理角色的所有组件和事件监听器
        
        del self.players[user_id]
        return True
    
    # 角色选择
    def select_role(self, user_id: int, role_id: int) -> bool:
        """选择角色"""
        if (user_id not in self.players or 
            self.state not in [GameState.WAITING, GameState.ROLE_SELECTION]):
            return False
        
        # 检查角色是否已被选择
        for player_role in self.players.values():
            if player_role and player_role.role_id == role_id:
                return False
        
        # 创建角色
        role = self.role_factory.create_role(role_id, user_id)
        if not role:
            return False
        
        # 设置角色的房间引用
        role.room_obj = self
        
        # 如果之前有角色，先清理
        if self.players[user_id]:
            old_role = self.players[user_id]
            # 清理旧角色
        
        self.players[user_id] = role
        
        # 检查是否所有玩家都选择了角色
        if self._all_players_selected_roles():
            self.state = GameState.ROLE_SELECTION
        
        return True
    
    def _all_players_selected_roles(self) -> bool:
        """检查是否所有玩家都选择了角色"""
        return all(role is not None for role in self.players.values())
    
    # 游戏开始
    def start_game(self) -> bool:
        """开始游戏"""
        if (self.state != GameState.ROLE_SELECTION or 
            not self._all_players_selected_roles() or
            len(self.players) < 2):
            return False
        
        self.state = GameState.PLAYING
        
        # 初始化玩家位置
        self._initialize_player_positions()
        
        # 设置回合顺序
        self.turn_order = list(self.players.keys())
        random.shuffle(self.turn_order)
        
        # 开始第一个回合
        self._start_next_turn()
        
        # 触发游戏开始事件
        fire_event(EventType.GAME_START, data={'game': self})
        
        return True
    
    def _initialize_player_positions(self):
        """初始化玩家位置"""
        positions = [0, 9, 18, 27]  # 四个角的位置
        for i, (user_id, role) in enumerate(self.players.items()):
            if i < len(positions):
                position = positions[i]
                role.position = position
                role.player_num = i
                self.runway[position]["players"].append(user_id)
    
    # 回合管理
    def _start_next_turn(self):
        """开始下一个回合"""
        if self.state != GameState.PLAYING:
            return
        
        # 找到下一个存活的玩家
        alive_players = [uid for uid, role in self.players.items() if role.is_alive()]
        
        if len(alive_players) <= 1:
            self._end_game()
            return
        
        # 更新回合顺序，只包含存活玩家
        self.turn_order = [uid for uid in self.turn_order if uid in alive_players]
        
        if not self.turn_order:
            self._end_game()
            return
        
        # 获取当前回合玩家
        if self.current_turn_player_id is None:
            self.current_turn_player_id = self.turn_order[0]
        else:
            current_index = self.turn_order.index(self.current_turn_player_id)
            next_index = (current_index + 1) % len(self.turn_order)
            self.current_turn_player_id = self.turn_order[next_index]
        
        # 设置玩家状态
        current_player = self.players[self.current_turn_player_id]
        current_player.change_state(RoleState.DICE_PHASE)
        
        # 触发回合开始事件
        fire_event(EventType.TURN_START, source=current_player)
    
    def end_turn(self):
        """结束当前回合"""
        if self.current_turn_player_id:
            current_player = self.players[self.current_turn_player_id]
            current_player.change_state(RoleState.WAITING)
            
            # 触发回合结束事件
            fire_event(EventType.TURN_END, source=current_player)
        
        self._start_next_turn()
    
    # 游戏操作
    def throw_dice(self, user_id: int) -> Optional[int]:
        """投掷骰子"""
        if (self.state != GameState.PLAYING or 
            user_id != self.current_turn_player_id or
            user_id not in self.players):
            return None
        
        player = self.players[user_id]
        if player.state != RoleState.DICE_PHASE:
            return None
        
        # 投掷骰子
        steps = random.randint(1, 8)
        
        # 移动玩家
        old_position = player.position
        player.move_relative(steps, self.runway)
        
        # 触发移动事件
        fire_event(EventType.AFTER_MOVE, 
                  source=player,
                  data={'steps': steps, 'old_position': old_position, 'new_position': player.position})
        
        # 处理跑道事件
        self._trigger_runway_event(player)
        
        # 更新游戏状态
        self.dice_count += 1
        self._update_round_bonuses()
        
        # 切换到技能阶段
        player.change_state(RoleState.SKILL_PHASE)
        
        return steps
    
    def use_skill(self, user_id: int, skill_index: int, target_id: Optional[int] = None) -> bool:
        """使用技能"""
        if (self.state != GameState.PLAYING or 
            user_id != self.current_turn_player_id or
            user_id not in self.players):
            return False
        
        player = self.players[user_id]
        if player.state != RoleState.SKILL_PHASE:
            return False
        
        # 获取技能
        skills = player.get_skills()
        if skill_index < 0 or skill_index >= len(skills):
            return False
        
        skill = skills[skill_index]
        
        # 获取目标
        target = None
        if target_id and target_id in self.players:
            target = self.players[target_id]
        
        # 使用技能
        success = skill.cast(target)
        
        if success:
            # 结束回合
            self.end_turn()
        
        return success
    
    def skip_skill(self, user_id: int) -> bool:
        """跳过技能阶段"""
        if (self.state != GameState.PLAYING or 
            user_id != self.current_turn_player_id or
            user_id not in self.players):
            return False
        
        player = self.players[user_id]
        if player.state != RoleState.SKILL_PHASE:
            return False
        
        # 结束回合
        self.end_turn()
        return True
    
    def surrender(self, user_id: int) -> bool:
        """投降"""
        if (self.state != GameState.PLAYING or 
            user_id not in self.players):
            return False
        
        player = self.players[user_id]
        self._eliminate_player(player)
        return True
    
    # 游戏事件处理
    def _trigger_runway_event(self, player: BaseRole):
        """触发跑道事件"""
        case_type = self.runway[player.position]["case"]
        
        # 触发跑道事件
        fire_event(EventType.RUNWAY_EVENT_TRIGGERED,
                  source=player,
                  data={'case_type': case_type, 'position': player.position})
        
        # 这里可以根据case_type处理不同的跑道事件
        # 暂时简化处理
        if case_type == 1:  # 生命值事件
            change = random.randint(-50, 100)
            player.change_attribute('current_health', change)
        elif case_type == 2:  # 攻击力事件
            change = random.randint(-20, 50)
            player.change_attribute('attack', change)
        elif case_type == 3:  # 防御力事件
            change = random.randint(-15, 30)
            player.change_attribute('defense', change)
        elif case_type == 4:  # TP事件
            change = random.randint(-10, 30)
            player.change_attribute('current_tp', change)
        elif case_type == 5:  # 移动事件
            extra_steps = random.randint(-2, 3)
            if extra_steps != 0:
                player.move_relative(extra_steps, self.runway)
    
    def _update_round_bonuses(self):
        """更新回合奖励"""
        alive_count = len([p for p in self.players.values() if p.is_alive()])
        
        # 每(存活玩家数+1)次骰子，增加攻击力和距离
        if self.dice_count % (alive_count + 1) == 0:
            for player in self.players.values():
                if player.is_alive():
                    player.change_attribute('attack', 10)
                    player.change_attribute('distance', 2)
    
    def _eliminate_player(self, player: BaseRole):
        """淘汰玩家"""
        player.change_state(RoleState.OUT)
        
        # 记录排名
        alive_count = len([p for p in self.players.values() if p.is_alive()])
        self.rankings[alive_count + 1] = player.user_id
        
        # 从跑道移除
        if player.position < len(self.runway):
            if player.user_id in self.runway[player.position]["players"]:
                self.runway[player.position]["players"].remove(player.user_id)
        
        # 触发死亡事件
        fire_event(EventType.PLAYER_DEATH, source=player)
        
        # 检查游戏是否结束
        alive_players = [p for p in self.players.values() if p.is_alive()]
        if len(alive_players) <= 1:
            self._end_game()
    
    def _end_game(self):
        """结束游戏"""
        self.state = GameState.ENDED
        
        # 记录最后的获胜者
        alive_players = [p for p in self.players.values() if p.is_alive()]
        if alive_players:
            self.rankings[1] = alive_players[0].user_id
            self.state = GameState.WON
        
        # 触发游戏结束事件
        fire_event(EventType.GAME_END, data={'game': self, 'rankings': self.rankings})
    
    # 信息获取
    def get_player_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取玩家信息"""
        if user_id not in self.players or not self.players[user_id]:
            return None
        
        player = self.players[user_id]
        return {
            'user_id': user_id,
            'role_id': player.role_id,
            'name': player.name,
            'position': player.position,
            'state': player.state.value,
            'attributes': {
                'health': f"{player.get_attribute('current_health'):.0f}/{player.get_attribute('max_health'):.0f}",
                'tp': f"{player.get_attribute('current_tp'):.0f}",
                'attack': f"{player.get_attribute('attack'):.0f}",
                'defense': f"{player.get_attribute('defense'):.0f}",
                'distance': f"{player.get_attribute('distance'):.0f}",
                'crit_rate': f"{player.get_attribute('crit_rate'):.0f}%",
            },
            'skills': [{'name': skill.name, 'description': skill.description, 'tp_cost': skill.tp_cost} 
                      for skill in player.get_skills()],
            'buffs': [{'name': buff.name, 'description': buff.description, 'duration': buff.remaining_duration}
                     for buff in player.get_components_by_type('buff')]
        }
    
    def get_game_state(self) -> Dict[str, Any]:
        """获取游戏状态"""
        return {
            'state': self.state,
            'current_turn_player': self.current_turn_player_id,
            'round_count': self.round_count,
            'dice_count': self.dice_count,
            'players': {uid: self.get_player_info(uid) for uid in self.players.keys()},
            'rankings': self.rankings
        }
