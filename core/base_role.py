# 基础角色类 - 所有角色的基类
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from enum import Enum
from .components import ComponentManager, Component, ComponentType, AttributeComponent
from .events import EventListener, EventType, GameEvent, fire_event, register_listener, unregister_listener
from ..attr import Attr


class RoleState(Enum):
    """角色状态枚举"""
    WAITING = "waiting"
    DICE_PHASE = "dice_phase"
    SKILL_PHASE = "skill_phase"
    OUT = "out"
    FAKE_OUT = "fake_out"  # 假死状态


class BaseRole(EventListener):
    """基础角色类"""
    
    def __init__(self, user_id: int, role_id: int, name: str):
        self.user_id = user_id
        self.role_id = role_id
        self.name = name
        
        # 游戏状态
        self.state = RoleState.WAITING
        self.position = 0
        self.player_num = 0
        self.room_obj = None
        
        # 组件管理器
        self.component_manager = ComponentManager(self)
        
        # 初始化基础属性
        self._init_base_attributes()
        
        # 注册为事件监听器
        self._register_event_listeners()
    
    def _init_base_attributes(self):
        """初始化基础属性"""
        # 创建基础属性组件
        attributes = [
            ('max_health', 'MAX_HEALTH', 1000),
            ('current_health', 'CURRENT_HEALTH', 1000),
            ('attack', 'ATTACK', 100),
            ('defense', 'DEFENSE', 50),
            ('distance', 'DISTANCE', 6),
            ('current_tp', 'CURRENT_TP', 0),
            ('max_tp', 'MAX_TP', 100),
            ('crit_rate', 'CRIT_RATE', 10),
            ('crit_damage', 'CRIT_DAMAGE', 2.0),
        ]
        
        for attr_id, attr_name, base_value in attributes:
            attr_component = AttributeComponent(attr_id, attr_name, base_value)
            self.component_manager.add_component(attr_component)
    
    def _register_event_listeners(self):
        """注册事件监听器"""
        register_listener(EventType.TURN_START, self)
        register_listener(EventType.TURN_END, self)
        register_listener(EventType.BEFORE_HURT, self)
        register_listener(EventType.AFTER_HURT, self)
    
    def get_priority(self) -> int:
        """事件监听器优先级"""
        return 200
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        if event.source != self:
            return
        
        if event.event_type == EventType.TURN_START:
            self._on_turn_start(event)
        elif event.event_type == EventType.TURN_END:
            self._on_turn_end(event)
        elif event.event_type == EventType.BEFORE_HURT:
            self._on_before_hurt(event)
        elif event.event_type == EventType.AFTER_HURT:
            self._on_after_hurt(event)
    
    def _on_turn_start(self, event: GameEvent):
        """回合开始处理"""
        # 增加TP
        self.change_attribute('current_tp', 10)
    
    def _on_turn_end(self, event: GameEvent):
        """回合结束处理"""
        pass
    
    def _on_before_hurt(self, event: GameEvent):
        """受伤前处理"""
        # 计算防御减伤
        damage = event.data.get('damage', 0)
        defense = self.get_attribute('defense')
        reduced_damage = self._calculate_damage_reduction(damage, defense)
        event.data['damage'] = reduced_damage
    
    def _on_after_hurt(self, event: GameEvent):
        """受伤后处理"""
        # 受伤回复TP
        damage = event.data.get('damage', 0)
        max_health = self.get_attribute('max_health')
        tp_gain = int(damage / max_health * 100 / 2)
        self.change_attribute('current_tp', tp_gain)
    
    def _calculate_damage_reduction(self, damage: float, defense: float) -> float:
        """计算防御减伤"""
        percent = 0.0
        if defense <= 100:
            percent = defense * 0.0015
        else:
            if defense <= 500:
                percent = 100 * 0.0015 + (defense - 100) * 0.0007
            elif 500 < defense <= 1000:
                percent = 100 * 0.0015 + 400 * 0.0007 + (defense - 500) * 0.0005
            else:
                percent = 100 * 0.0015 + 400 * 0.0007 + 500 * 0.0005
        
        return damage * (1 - percent)
    
    # 属性相关方法
    def get_attribute(self, attr_name: str) -> float:
        """获取属性值"""
        attr_component = self.component_manager.get_component(attr_name)
        if isinstance(attr_component, AttributeComponent):
            return attr_component.get_final_value()
        return 0
    
    def set_base_attribute(self, attr_name: str, value: float):
        """设置基础属性值"""
        attr_component = self.component_manager.get_component(attr_name)
        if isinstance(attr_component, AttributeComponent):
            attr_component.set_base_value(value)
    
    def change_attribute(self, attr_name: str, delta: float):
        """改变属性值"""
        current_value = self.get_attribute(attr_name)
        new_value = current_value + delta
        
        # 处理特殊属性限制
        if attr_name == 'current_health':
            max_health = self.get_attribute('max_health')
            new_value = max(0, min(new_value, max_health))
            if new_value <= 0:
                self._handle_death()
        elif attr_name == 'current_tp':
            max_tp = self.get_attribute('max_tp')
            new_value = max(0, min(new_value, max_tp))
        elif attr_name in ['distance', 'crit_rate']:
            new_value = max(0, new_value)
            if attr_name == 'distance':
                new_value = min(new_value, 15)  # 最大攻击距离
            elif attr_name == 'crit_rate':
                new_value = min(new_value, 100)  # 最大暴击率
        
        self.set_base_attribute(attr_name, new_value)
        
        # 触发属性变化事件
        fire_event(EventType.ATTR_CHANGED, 
                  source=self, 
                  data={'attribute': attr_name, 'old_value': current_value, 'new_value': new_value})
    
    def _handle_death(self):
        """处理死亡"""
        self.state = RoleState.OUT
        fire_event(EventType.PLAYER_DEATH, source=self)
    
    # 组件相关方法
    def add_component(self, component: Component):
        """添加组件"""
        self.component_manager.add_component(component)
    
    def remove_component(self, component_id: str):
        """移除组件"""
        self.component_manager.remove_component(component_id)
    
    def get_component(self, component_id: str) -> Optional[Component]:
        """获取组件"""
        return self.component_manager.get_component(component_id)
    
    def get_components_by_type(self, component_type: ComponentType) -> List[Component]:
        """按类型获取组件"""
        return self.component_manager.get_components_by_type(component_type)
    
    def has_component(self, component_id: str) -> bool:
        """检查是否有指定组件"""
        return self.component_manager.has_component(component_id)
    
    # 技能相关方法
    def get_skills(self) -> List[Component]:
        """获取所有技能"""
        return self.get_components_by_type(ComponentType.SKILL)
    
    def can_cast_skill(self, skill_id: str, target=None) -> bool:
        """检查是否可以释放技能"""
        skill = self.get_component(skill_id)
        if not skill or skill.get_component_type() != ComponentType.SKILL:
            return False
        return skill.can_cast(target)
    
    def cast_skill(self, skill_id: str, target=None) -> bool:
        """释放技能"""
        skill = self.get_component(skill_id)
        if not skill or skill.get_component_type() != ComponentType.SKILL:
            return False
        return skill.cast(target)
    
    # 状态相关方法
    def change_state(self, new_state: RoleState):
        """改变角色状态"""
        old_state = self.state
        self.state = new_state
        fire_event(EventType.TURN_START if new_state == RoleState.DICE_PHASE else EventType.TURN_END,
                  source=self,
                  data={'old_state': old_state, 'new_state': new_state})
    
    def is_alive(self) -> bool:
        """检查是否存活"""
        return self.state not in [RoleState.OUT, RoleState.FAKE_OUT]
    
    def is_out(self) -> bool:
        """检查是否出局"""
        return self.state == RoleState.OUT
    
    # 位置相关方法
    def move_to_position(self, new_position: int, runway: List[Dict]):
        """移动到新位置"""
        if self.state in [RoleState.OUT, RoleState.FAKE_OUT]:
            return
        
        old_position = self.position
        
        # 从旧位置移除
        if old_position < len(runway):
            runway[old_position]["players"].remove(self.user_id)
        
        # 处理环形跑道
        while new_position >= len(runway) or new_position < 0:
            if new_position >= len(runway):
                new_position -= len(runway)
            elif new_position < 0:
                new_position += len(runway)
        
        # 移动到新位置
        self.position = new_position
        runway[new_position]["players"].append(self.user_id)
        
        # 触发位置变化事件
        fire_event(EventType.POSITION_CHANGED,
                  source=self,
                  data={'old_position': old_position, 'new_position': new_position})
    
    def move_relative(self, steps: int, runway: List[Dict]):
        """相对移动"""
        new_position = self.position + steps
        self.move_to_position(new_position, runway)
    
    # 抽象方法 - 子类必须实现
    @abstractmethod
    def get_role_position(self) -> str:
        """获取角色定位"""
        pass
    
    @abstractmethod
    def init_role_data(self, role_data: Dict[str, Any]):
        """初始化角色数据"""
        pass
    
    def __del__(self):
        """析构函数 - 清理事件监听器"""
        try:
            unregister_listener(EventType.TURN_START, self)
            unregister_listener(EventType.TURN_END, self)
            unregister_listener(EventType.BEFORE_HURT, self)
            unregister_listener(EventType.AFTER_HURT, self)
        except:
            pass
