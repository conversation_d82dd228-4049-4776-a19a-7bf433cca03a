# 角色工厂和注册系统
from abc import ABC, abstractmethod
from typing import Dict, Type, Optional, Any, List
import importlib
import inspect
from .base_role import BaseRole
from .components import SkillComponent, PassiveComponent, ComponentType


class RoleRegistry:
    """角色注册器 - 单例模式"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        self._role_classes: Dict[int, Type[BaseRole]] = {}
        self._role_configs: Dict[int, Dict[str, Any]] = {}
        self._initialized = True
    
    def register_role(self, role_id: int, role_class: Type[BaseRole], config: Dict[str, Any] = None):
        """注册角色类"""
        if not issubclass(role_class, BaseRole):
            raise ValueError(f"Role class {role_class} must inherit from BaseRole")
        
        self._role_classes[role_id] = role_class
        self._role_configs[role_id] = config or {}
    
    def unregister_role(self, role_id: int):
        """注销角色"""
        if role_id in self._role_classes:
            del self._role_classes[role_id]
        if role_id in self._role_configs:
            del self._role_configs[role_id]
    
    def get_role_class(self, role_id: int) -> Optional[Type[BaseRole]]:
        """获取角色类"""
        return self._role_classes.get(role_id)
    
    def get_role_config(self, role_id: int) -> Optional[Dict[str, Any]]:
        """获取角色配置"""
        return self._role_configs.get(role_id)
    
    def get_all_role_ids(self) -> List[int]:
        """获取所有已注册的角色ID"""
        return list(self._role_classes.keys())
    
    def is_role_registered(self, role_id: int) -> bool:
        """检查角色是否已注册"""
        return role_id in self._role_classes
    
    def auto_register_from_module(self, module_name: str):
        """从模块自动注册角色"""
        try:
            module = importlib.import_module(module_name)
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, BaseRole) and 
                    obj != BaseRole and
                    hasattr(obj, 'ROLE_ID')):
                    
                    role_id = obj.ROLE_ID
                    config = getattr(obj, 'ROLE_CONFIG', {})
                    self.register_role(role_id, obj, config)
        except ImportError as e:
            print(f"Failed to import module {module_name}: {e}")


class RoleFactory:
    """角色工厂"""
    
    def __init__(self):
        self.registry = RoleRegistry()
    
    def create_role(self, role_id: int, user_id: int, **kwargs) -> Optional[BaseRole]:
        """创建角色实例"""
        role_class = self.registry.get_role_class(role_id)
        if not role_class:
            return None
        
        config = self.registry.get_role_config(role_id)
        
        try:
            # 创建角色实例
            role = role_class(user_id, role_id, config.get('name', f'Role_{role_id}'))
            
            # 初始化角色数据
            role.init_role_data(config)
            
            return role
        except Exception as e:
            print(f"Failed to create role {role_id}: {e}")
            return None
    
    def get_available_roles(self) -> List[Dict[str, Any]]:
        """获取可用角色列表"""
        roles = []
        for role_id in self.registry.get_all_role_ids():
            config = self.registry.get_role_config(role_id)
            if config:
                roles.append({
                    'id': role_id,
                    'name': config.get('name', f'Role_{role_id}'),
                    'position': config.get('position', 'Unknown'),
                    'description': config.get('description', ''),
                    'health': config.get('health', 1000),
                    'attack': config.get('attack', 100),
                    'defense': config.get('defense', 50),
                    'distance': config.get('distance', 6),
                    'tp': config.get('tp', 0),
                    'crit': config.get('crit', 10),
                })
        return roles


class RoleBuilder:
    """角色构建器 - 用于简化角色创建过程"""
    
    def __init__(self, role_id: int, name: str):
        self.role_id = role_id
        self.name = name
        self.config = {
            'name': name,
            'health': 1000,
            'attack': 100,
            'defense': 50,
            'distance': 6,
            'tp': 0,
            'crit': 10,
            'position': 'Unknown',
            'skills': [],
            'passives': [],
        }
    
    def set_stats(self, health: int = None, attack: int = None, defense: int = None, 
                  distance: int = None, tp: int = None, crit: int = None):
        """设置基础属性"""
        if health is not None:
            self.config['health'] = health
        if attack is not None:
            self.config['attack'] = attack
        if defense is not None:
            self.config['defense'] = defense
        if distance is not None:
            self.config['distance'] = distance
        if tp is not None:
            self.config['tp'] = tp
        if crit is not None:
            self.config['crit'] = crit
        return self
    
    def set_position(self, position: str):
        """设置角色定位"""
        self.config['position'] = position
        return self
    
    def add_skill(self, skill_config: Dict[str, Any]):
        """添加技能"""
        self.config['skills'].append(skill_config)
        return self
    
    def add_passive(self, passive_config: Dict[str, Any]):
        """添加被动技能"""
        self.config['passives'].append(passive_config)
        return self
    
    def build_config(self) -> Dict[str, Any]:
        """构建配置"""
        return self.config.copy()


def role_decorator(role_id: int, **config):
    """角色装饰器 - 用于自动注册角色"""
    def decorator(cls):
        if not issubclass(cls, BaseRole):
            raise ValueError(f"Class {cls} must inherit from BaseRole")
        
        # 设置角色ID和配置
        cls.ROLE_ID = role_id
        cls.ROLE_CONFIG = config
        
        # 自动注册到注册器
        registry = RoleRegistry()
        registry.register_role(role_id, cls, config)
        
        return cls
    return decorator


# 便利函数
def register_role(role_id: int, role_class: Type[BaseRole], config: Dict[str, Any] = None):
    """注册角色的便利函数"""
    registry = RoleRegistry()
    registry.register_role(role_id, role_class, config)


def create_role(role_id: int, user_id: int, **kwargs) -> Optional[BaseRole]:
    """创建角色的便利函数"""
    factory = RoleFactory()
    return factory.create_role(role_id, user_id, **kwargs)


def get_available_roles() -> List[Dict[str, Any]]:
    """获取可用角色的便利函数"""
    factory = RoleFactory()
    return factory.get_available_roles()


def auto_register_roles_from_directory(directory: str):
    """从目录自动注册所有角色"""
    import os
    import glob
    
    registry = RoleRegistry()
    
    # 查找所有Python文件
    pattern = os.path.join(directory, "*.py")
    for file_path in glob.glob(pattern):
        if os.path.basename(file_path) == "__init__.py":
            continue
        
        # 构建模块名
        module_name = os.path.splitext(os.path.basename(file_path))[0]
        full_module_name = f"{directory.replace('/', '.')}.{module_name}"
        
        registry.auto_register_from_module(full_module_name)
