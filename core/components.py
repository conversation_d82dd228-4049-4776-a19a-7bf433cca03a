# 组件系统 - 将角色能力模块化
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from enum import Enum
from .events import EventListener, EventType, GameEvent

if TYPE_CHECKING:
    from .base_role import BaseRole


class ComponentType(Enum):
    """组件类型枚举"""
    SKILL = "skill"
    BUFF = "buff"
    PASSIVE = "passive"
    ATTRIBUTE = "attribute"
    SPECIAL = "special"


class Component(ABC):
    """组件基类"""
    
    def __init__(self, component_id: str, name: str, description: str = ""):
        self.component_id = component_id
        self.name = name
        self.description = description
        self.owner: Optional['BaseRole'] = None
        self.enabled = True
    
    @abstractmethod
    def get_component_type(self) -> ComponentType:
        """获取组件类型"""
        pass
    
    def attach_to_role(self, role: 'BaseRole'):
        """附加到角色"""
        self.owner = role
        self.on_attach()
    
    def detach_from_role(self):
        """从角色分离"""
        self.on_detach()
        self.owner = None
    
    def on_attach(self):
        """附加时的回调"""
        pass
    
    def on_detach(self):
        """分离时的回调"""
        pass
    
    def enable(self):
        """启用组件"""
        self.enabled = True
    
    def disable(self):
        """禁用组件"""
        self.enabled = False


class SkillComponent(Component, EventListener):
    """技能组件基类"""
    
    def __init__(self, skill_id: str, name: str, description: str, tp_cost: int, 
                 cooldown: int = 0, max_uses: int = -1):
        super().__init__(skill_id, name, description)
        self.tp_cost = tp_cost
        self.cooldown = cooldown
        self.max_uses = max_uses
        self.current_cooldown = 0
        self.uses_remaining = max_uses
    
    def get_component_type(self) -> ComponentType:
        return ComponentType.SKILL
    
    @abstractmethod
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放技能"""
        pass
    
    @abstractmethod
    def cast(self, target=None) -> bool:
        """释放技能"""
        pass
    
    def get_priority(self) -> int:
        """事件监听器优先级"""
        return 100
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        if not self.enabled or not self.owner:
            return
        
        if event.event_type == EventType.TURN_END and event.source == self.owner:
            self._reduce_cooldown()
    
    def _reduce_cooldown(self):
        """减少冷却时间"""
        if self.current_cooldown > 0:
            self.current_cooldown -= 1
    
    def start_cooldown(self):
        """开始冷却"""
        self.current_cooldown = self.cooldown
        if self.max_uses > 0:
            self.uses_remaining -= 1


class BuffComponent(Component, EventListener):
    """Buff组件基类"""
    
    def __init__(self, buff_id: str, name: str, description: str, 
                 duration: int = -1, stack_limit: int = 1):
        super().__init__(buff_id, name, description)
        self.duration = duration  # -1表示永久
        self.stack_limit = stack_limit
        self.current_stacks = 1
        self.remaining_duration = duration
    
    def get_component_type(self) -> ComponentType:
        return ComponentType.BUFF
    
    @abstractmethod
    def apply_effect(self):
        """应用buff效果"""
        pass
    
    @abstractmethod
    def remove_effect(self):
        """移除buff效果"""
        pass
    
    def get_priority(self) -> int:
        """事件监听器优先级"""
        return 50
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        if not self.enabled or not self.owner:
            return
        
        if event.event_type == EventType.TURN_END and event.source == self.owner:
            self._reduce_duration()
    
    def _reduce_duration(self):
        """减少持续时间"""
        if self.remaining_duration > 0:
            self.remaining_duration -= 1
            if self.remaining_duration <= 0:
                self.owner.remove_component(self)
    
    def add_stack(self, stacks: int = 1):
        """增加层数"""
        self.current_stacks = min(self.current_stacks + stacks, self.stack_limit)
    
    def remove_stack(self, stacks: int = 1):
        """减少层数"""
        self.current_stacks = max(0, self.current_stacks - stacks)
        if self.current_stacks <= 0:
            self.owner.remove_component(self)


class PassiveComponent(Component, EventListener):
    """被动技能组件基类"""
    
    def __init__(self, passive_id: str, name: str, description: str):
        super().__init__(passive_id, name, description)
    
    def get_component_type(self) -> ComponentType:
        return ComponentType.PASSIVE
    
    @abstractmethod
    def apply_passive_effect(self):
        """应用被动效果"""
        pass
    
    def get_priority(self) -> int:
        """事件监听器优先级"""
        return 10
    
    @abstractmethod
    def handle_event(self, event: GameEvent):
        """处理事件 - 子类必须实现"""
        pass


class AttributeComponent(Component):
    """属性组件"""
    
    def __init__(self, attr_id: str, name: str, base_value: float = 0):
        super().__init__(attr_id, name)
        self.base_value = base_value
        self.modifiers: List[Dict[str, Any]] = []  # 属性修饰符
    
    def get_component_type(self) -> ComponentType:
        return ComponentType.ATTRIBUTE
    
    def get_final_value(self) -> float:
        """获取最终属性值（基础值 + 所有修饰符）"""
        final_value = self.base_value
        
        # 应用加法修饰符
        for modifier in self.modifiers:
            if modifier['type'] == 'add':
                final_value += modifier['value']
        
        # 应用乘法修饰符
        for modifier in self.modifiers:
            if modifier['type'] == 'multiply':
                final_value *= modifier['value']
        
        return final_value
    
    def add_modifier(self, modifier_type: str, value: float, source: str = ""):
        """添加属性修饰符"""
        modifier = {
            'type': modifier_type,
            'value': value,
            'source': source
        }
        self.modifiers.append(modifier)
    
    def remove_modifier(self, source: str):
        """移除指定来源的修饰符"""
        self.modifiers = [m for m in self.modifiers if m['source'] != source]
    
    def set_base_value(self, value: float):
        """设置基础值"""
        old_value = self.get_final_value()
        self.base_value = value
        new_value = self.get_final_value()
        
        if self.owner and old_value != new_value:
            from .events import fire_event
            fire_event(EventType.ATTR_CHANGED, 
                      source=self.owner, 
                      data={'attribute': self.component_id, 'old_value': old_value, 'new_value': new_value})


class ComponentManager:
    """组件管理器"""
    
    def __init__(self, owner: 'BaseRole'):
        self.owner = owner
        self.components: Dict[str, Component] = {}
        self.components_by_type: Dict[ComponentType, List[Component]] = {
            component_type: [] for component_type in ComponentType
        }
    
    def add_component(self, component: Component):
        """添加组件"""
        if component.component_id in self.components:
            self.remove_component(component.component_id)
        
        self.components[component.component_id] = component
        self.components_by_type[component.get_component_type()].append(component)
        component.attach_to_role(self.owner)
    
    def remove_component(self, component_id: str):
        """移除组件"""
        if component_id not in self.components:
            return
        
        component = self.components[component_id]
        component.detach_from_role()
        
        del self.components[component_id]
        self.components_by_type[component.get_component_type()].remove(component)
    
    def get_component(self, component_id: str) -> Optional[Component]:
        """获取组件"""
        return self.components.get(component_id)
    
    def get_components_by_type(self, component_type: ComponentType) -> List[Component]:
        """按类型获取组件"""
        return [c for c in self.components_by_type[component_type] if c.enabled]
    
    def has_component(self, component_id: str) -> bool:
        """检查是否有指定组件"""
        return component_id in self.components
    
    def clear_components_by_type(self, component_type: ComponentType):
        """清除指定类型的所有组件"""
        components_to_remove = list(self.components_by_type[component_type])
        for component in components_to_remove:
            self.remove_component(component.component_id)
