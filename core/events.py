# 事件系统 - 用于解耦各个模块之间的依赖关系
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Callable
from enum import Enum
import weakref


class EventType(Enum):
    """游戏事件类型枚举"""
    # 回合事件
    TURN_START = "turn_start"
    TURN_END = "turn_end"
    ROUND_START = "round_start"
    ROUND_END = "round_end"
    
    # 战斗事件
    BEFORE_ATTACK = "before_attack"
    AFTER_ATTACK = "after_attack"
    BEFORE_HURT = "before_hurt"
    AFTER_HURT = "after_hurt"
    BEFORE_HEAL = "before_heal"
    AFTER_HEAL = "after_heal"
    
    # 技能事件
    SKILL_CAST = "skill_cast"
    SKILL_HIT = "skill_hit"
    SKILL_MISS = "skill_miss"
    
    # 移动事件
    BEFORE_MOVE = "before_move"
    AFTER_MOVE = "after_move"
    POSITION_CHANGED = "position_changed"
    
    # 属性事件
    ATTR_CHANGED = "attr_changed"
    HP_CHANGED = "hp_changed"
    TP_CHANGED = "tp_changed"
    
    # Buff事件
    BUFF_ADDED = "buff_added"
    BUFF_REMOVED = "buff_removed"
    BUFF_TRIGGERED = "buff_triggered"
    
    # 游戏状态事件
    PLAYER_DEATH = "player_death"
    PLAYER_REVIVE = "player_revive"
    GAME_START = "game_start"
    GAME_END = "game_end"
    
    # 跑道事件
    RUNWAY_EVENT_TRIGGERED = "runway_event_triggered"


class GameEvent:
    """游戏事件基类"""
    def __init__(self, event_type: EventType, source=None, target=None, data: Dict[str, Any] = None):
        self.event_type = event_type
        self.source = source  # 事件源
        self.target = target  # 事件目标
        self.data = data or {}  # 事件数据
        self.cancelled = False  # 是否被取消
        
    def cancel(self):
        """取消事件"""
        self.cancelled = True
        
    def is_cancelled(self) -> bool:
        """检查事件是否被取消"""
        return self.cancelled


class EventListener(ABC):
    """事件监听器抽象基类"""
    
    @abstractmethod
    def handle_event(self, event: GameEvent) -> None:
        """处理事件"""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """获取优先级，数字越小优先级越高"""
        return 0


class EventManager:
    """事件管理器 - 单例模式"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        self._listeners: Dict[EventType, List[weakref.ref]] = {}
        self._initialized = True
    
    def register_listener(self, event_type: EventType, listener: EventListener):
        """注册事件监听器"""
        if event_type not in self._listeners:
            self._listeners[event_type] = []
        
        # 使用弱引用避免内存泄漏
        listener_ref = weakref.ref(listener, self._cleanup_listener)
        self._listeners[event_type].append(listener_ref)
        
        # 按优先级排序
        self._listeners[event_type].sort(key=lambda ref: ref().get_priority() if ref() else float('inf'))
    
    def unregister_listener(self, event_type: EventType, listener: EventListener):
        """注销事件监听器"""
        if event_type in self._listeners:
            self._listeners[event_type] = [
                ref for ref in self._listeners[event_type] 
                if ref() is not None and ref() is not listener
            ]
    
    def _cleanup_listener(self, listener_ref):
        """清理失效的监听器引用"""
        for event_type, listeners in self._listeners.items():
            self._listeners[event_type] = [ref for ref in listeners if ref is not listener_ref]
    
    def fire_event(self, event: GameEvent) -> GameEvent:
        """触发事件"""
        event_type = event.event_type
        if event_type not in self._listeners:
            return event
        
        # 清理失效的引用并获取有效的监听器
        valid_listeners = []
        for listener_ref in self._listeners[event_type]:
            listener = listener_ref()
            if listener is not None:
                valid_listeners.append(listener)
        
        # 更新监听器列表
        self._listeners[event_type] = [
            ref for ref in self._listeners[event_type] 
            if ref() is not None
        ]
        
        # 按优先级顺序处理事件
        for listener in valid_listeners:
            if event.is_cancelled():
                break
            try:
                listener.handle_event(event)
            except Exception as e:
                print(f"Error handling event {event_type}: {e}")
        
        return event
    
    def clear_all_listeners(self):
        """清除所有监听器"""
        self._listeners.clear()


# 便利函数
def fire_event(event_type: EventType, source=None, target=None, data: Dict[str, Any] = None) -> GameEvent:
    """触发事件的便利函数"""
    event = GameEvent(event_type, source, target, data)
    return EventManager().fire_event(event)


def register_listener(event_type: EventType, listener: EventListener):
    """注册监听器的便利函数"""
    EventManager().register_listener(event_type, listener)


def unregister_listener(event_type: EventType, listener: EventListener):
    """注销监听器的便利函数"""
    EventManager().unregister_listener(event_type, listener)
