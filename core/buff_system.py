# 重构后的Buff系统
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from enum import Enum
import json
from .components import BuffComponent, ComponentType
from .events import EventType, GameEvent, fire_event, EventListener

if TYPE_CHECKING:
    from .base_role import BaseRole


class BuffTriggerType(Enum):
    """Buff触发类型"""
    IMMEDIATE = "immediate"  # 立即触发
    TURN_START = "turn_start"  # 回合开始
    TURN_END = "turn_end"  # 回合结束
    BEFORE_ATTACK = "before_attack"  # 攻击前
    AFTER_ATTACK = "after_attack"  # 攻击后
    BEFORE_HURT = "before_hurt"  # 受伤前
    AFTER_HURT = "after_hurt"  # 受伤后
    ON_DEATH = "on_death"  # 死亡时
    ON_HEAL = "on_heal"  # 治疗时
    ON_MOVE = "on_move"  # 移动时


class BuffEffectType(Enum):
    """Buff效果类型"""
    ATTRIBUTE_MODIFIER = "attribute_modifier"  # 属性修饰符
    DAMAGE_SHIELD = "damage_shield"  # 伤害护盾
    DAMAGE_REFLECTION = "damage_reflection"  # 伤害反射
    HEAL_OVER_TIME = "heal_over_time"  # 持续治疗
    DAMAGE_OVER_TIME = "damage_over_time"  # 持续伤害
    IMMUNITY = "immunity"  # 免疫
    CUSTOM = "custom"  # 自定义效果


class BuffTemplate:
    """Buff模板"""
    
    def __init__(self, template_id: str, name: str, description: str,
                 effect_type: BuffEffectType, trigger_type: BuffTriggerType,
                 duration: int = -1, stack_limit: int = 1, 
                 effect_config: Dict[str, Any] = None):
        self.template_id = template_id
        self.name = name
        self.description = description
        self.effect_type = effect_type
        self.trigger_type = trigger_type
        self.duration = duration
        self.stack_limit = stack_limit
        self.effect_config = effect_config or {}
    
    def create_buff(self) -> 'ConfigurableBuff':
        """创建Buff实例"""
        return ConfigurableBuff(self)


class ConfigurableBuff(BuffComponent):
    """可配置的Buff组件"""
    
    def __init__(self, template: BuffTemplate):
        super().__init__(
            buff_id=template.template_id,
            name=template.name,
            description=template.description,
            duration=template.duration,
            stack_limit=template.stack_limit
        )
        self.template = template
        self.effect_value = template.effect_config.get('value', 0)
        self.applied_modifiers: List[str] = []  # 记录已应用的修饰符
    
    def apply_effect(self):
        """应用buff效果"""
        if not self.owner:
            return
        
        if self.template.effect_type == BuffEffectType.ATTRIBUTE_MODIFIER:
            self._apply_attribute_modifier()
        elif self.template.effect_type == BuffEffectType.DAMAGE_SHIELD:
            self._apply_damage_shield()
        elif self.template.effect_type == BuffEffectType.HEAL_OVER_TIME:
            self._apply_heal_over_time()
        elif self.template.effect_type == BuffEffectType.DAMAGE_OVER_TIME:
            self._apply_damage_over_time()
        
        # 触发Buff应用事件
        fire_event(EventType.BUFF_ADDED, 
                  source=self.owner,
                  data={'buff': self, 'stacks': self.current_stacks})
    
    def remove_effect(self):
        """移除buff效果"""
        if not self.owner:
            return
        
        if self.template.effect_type == BuffEffectType.ATTRIBUTE_MODIFIER:
            self._remove_attribute_modifier()
        
        # 触发Buff移除事件
        fire_event(EventType.BUFF_REMOVED, 
                  source=self.owner,
                  data={'buff': self})
    
    def _apply_attribute_modifier(self):
        """应用属性修饰符"""
        attr_name = self.template.effect_config.get('attribute')
        modifier_type = self.template.effect_config.get('modifier_type', 'add')
        value = self.effect_value * self.current_stacks
        
        if attr_name:
            attr_component = self.owner.get_component(attr_name)
            if attr_component and hasattr(attr_component, 'add_modifier'):
                modifier_id = f"{self.component_id}_{self.owner.user_id}"
                attr_component.add_modifier(modifier_type, value, modifier_id)
                self.applied_modifiers.append(modifier_id)
    
    def _remove_attribute_modifier(self):
        """移除属性修饰符"""
        attr_name = self.template.effect_config.get('attribute')
        if attr_name:
            attr_component = self.owner.get_component(attr_name)
            if attr_component and hasattr(attr_component, 'remove_modifier'):
                for modifier_id in self.applied_modifiers:
                    attr_component.remove_modifier(modifier_id)
                self.applied_modifiers.clear()
    
    def _apply_damage_shield(self):
        """应用伤害护盾"""
        # 护盾效果通过事件处理
        pass
    
    def _apply_heal_over_time(self):
        """应用持续治疗"""
        heal_amount = self.effect_value * self.current_stacks
        self.owner.change_attribute('current_health', heal_amount)
    
    def _apply_damage_over_time(self):
        """应用持续伤害"""
        damage_amount = self.effect_value * self.current_stacks
        self.owner.change_attribute('current_health', -damage_amount)
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        super().handle_event(event)
        
        if not self.enabled or not self.owner:
            return
        
        # 根据触发类型处理事件
        should_trigger = False
        
        if (self.template.trigger_type == BuffTriggerType.TURN_START and 
            event.event_type == EventType.TURN_START and event.source == self.owner):
            should_trigger = True
        elif (self.template.trigger_type == BuffTriggerType.TURN_END and 
              event.event_type == EventType.TURN_END and event.source == self.owner):
            should_trigger = True
        elif (self.template.trigger_type == BuffTriggerType.BEFORE_HURT and 
              event.event_type == EventType.BEFORE_HURT and event.target == self.owner):
            should_trigger = True
            self._handle_before_hurt(event)
        elif (self.template.trigger_type == BuffTriggerType.AFTER_HURT and 
              event.event_type == EventType.AFTER_HURT and event.target == self.owner):
            should_trigger = True
        
        if should_trigger:
            self._trigger_effect(event)
    
    def _trigger_effect(self, event: GameEvent):
        """触发效果"""
        if self.template.effect_type == BuffEffectType.HEAL_OVER_TIME:
            self._apply_heal_over_time()
        elif self.template.effect_type == BuffEffectType.DAMAGE_OVER_TIME:
            self._apply_damage_over_time()
        
        # 触发Buff触发事件
        fire_event(EventType.BUFF_TRIGGERED, 
                  source=self.owner,
                  data={'buff': self, 'trigger_event': event})
    
    def _handle_before_hurt(self, event: GameEvent):
        """处理受伤前事件"""
        if self.template.effect_type == BuffEffectType.DAMAGE_SHIELD:
            damage = event.data.get('damage', 0)
            shield_value = self.effect_value * self.current_stacks
            
            if shield_value > 0:
                absorbed = min(damage, shield_value)
                event.data['damage'] = damage - absorbed
                self.effect_value -= absorbed / self.current_stacks
                
                if self.effect_value <= 0:
                    self.owner.remove_component(self.component_id)


class BuffManager:
    """Buff管理器"""
    
    def __init__(self):
        self.buff_templates: Dict[str, BuffTemplate] = {}
    
    def register_buff_template(self, template: BuffTemplate):
        """注册Buff模板"""
        self.buff_templates[template.template_id] = template
    
    def create_buff(self, template_id: str) -> Optional[ConfigurableBuff]:
        """创建Buff"""
        template = self.buff_templates.get(template_id)
        if template:
            return template.create_buff()
        return None
    
    def load_buffs_from_config(self, config_path: str):
        """从配置文件加载Buff"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            for buff_config in config.get('buffs', []):
                template = self._create_template_from_config(buff_config)
                if template:
                    self.register_buff_template(template)
        except Exception as e:
            print(f"Failed to load buffs from {config_path}: {e}")
    
    def _create_template_from_config(self, config: Dict[str, Any]) -> Optional[BuffTemplate]:
        """从配置创建Buff模板"""
        try:
            effect_type = BuffEffectType(config.get('effect_type', 'attribute_modifier'))
            trigger_type = BuffTriggerType(config.get('trigger_type', 'immediate'))
            
            return BuffTemplate(
                template_id=config['id'],
                name=config['name'],
                description=config.get('description', ''),
                effect_type=effect_type,
                trigger_type=trigger_type,
                duration=config.get('duration', -1),
                stack_limit=config.get('stack_limit', 1),
                effect_config=config.get('effect_config', {})
            )
        except Exception as e:
            print(f"Failed to create buff template from config: {e}")
            return None


# 预定义的常用Buff模板
def create_common_buff_templates():
    """创建常用的Buff模板"""
    templates = []
    
    # 攻击力增加Buff
    templates.append(BuffTemplate(
        template_id="attack_boost",
        name="攻击力提升",
        description="增加攻击力",
        effect_type=BuffEffectType.ATTRIBUTE_MODIFIER,
        trigger_type=BuffTriggerType.IMMEDIATE,
        duration=3,
        stack_limit=5,
        effect_config={
            'attribute': 'attack',
            'modifier_type': 'add',
            'value': 50
        }
    ))
    
    # 防御力增加Buff
    templates.append(BuffTemplate(
        template_id="defense_boost",
        name="防御力提升",
        description="增加防御力",
        effect_type=BuffEffectType.ATTRIBUTE_MODIFIER,
        trigger_type=BuffTriggerType.IMMEDIATE,
        duration=3,
        stack_limit=5,
        effect_config={
            'attribute': 'defense',
            'modifier_type': 'add',
            'value': 30
        }
    ))
    
    # 持续治疗Buff
    templates.append(BuffTemplate(
        template_id="regeneration",
        name="生命恢复",
        description="每回合恢复生命值",
        effect_type=BuffEffectType.HEAL_OVER_TIME,
        trigger_type=BuffTriggerType.TURN_END,
        duration=5,
        stack_limit=3,
        effect_config={'value': 100}
    ))
    
    # 持续伤害Buff
    templates.append(BuffTemplate(
        template_id="poison",
        name="中毒",
        description="每回合受到伤害",
        effect_type=BuffEffectType.DAMAGE_OVER_TIME,
        trigger_type=BuffTriggerType.TURN_END,
        duration=3,
        stack_limit=5,
        effect_config={'value': 50}
    ))
    
    return templates


# 全局Buff管理器实例
buff_manager = BuffManager()

# 注册常用Buff模板
for template in create_common_buff_templates():
    buff_manager.register_buff_template(template)


def register_buff_template(template: BuffTemplate):
    """注册Buff模板的便利函数"""
    buff_manager.register_buff_template(template)


def create_buff(template_id: str) -> Optional[ConfigurableBuff]:
    """创建Buff的便利函数"""
    return buff_manager.create_buff(template_id)


def load_buffs_from_config(config_path: str):
    """从配置文件加载Buff的便利函数"""
    buff_manager.load_buffs_from_config(config_path)
