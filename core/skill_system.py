# 重构后的技能系统
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from enum import Enum
import json
from .components import SkillComponent, ComponentType
from .effects import SkillEffect, EffectFactory
from .events import EventType, GameEvent, fire_event

if TYPE_CHECKING:
    from .base_role import BaseRole


class TargetType(Enum):
    """目标类型枚举"""
    SELF = "self"
    SELECT = "select"
    SELECT_EXCEPT_SELF = "select_except_self"
    ALL = "all"
    ALL_EXCEPT_SELF = "all_except_self"
    NEAREST = "nearest"
    RANDOM = "random"


class SkillTemplate:
    """技能模板 - 用于从配置创建技能"""
    
    def __init__(self, template_id: str, name: str, description: str, 
                 tp_cost: int, target_type: TargetType, effects: List[Dict[str, Any]],
                 cooldown: int = 0, max_uses: int = -1, distance_check: bool = True):
        self.template_id = template_id
        self.name = name
        self.description = description
        self.tp_cost = tp_cost
        self.target_type = target_type
        self.effects = effects
        self.cooldown = cooldown
        self.max_uses = max_uses
        self.distance_check = distance_check
    
    def create_skill(self) -> 'ConfigurableSkill':
        """创建技能实例"""
        return ConfigurableSkill(self)


class ConfigurableSkill(SkillComponent):
    """可配置的技能组件"""
    
    def __init__(self, template: SkillTemplate):
        super().__init__(
            skill_id=template.template_id,
            name=template.name,
            description=template.description,
            tp_cost=template.tp_cost,
            cooldown=template.cooldown,
            max_uses=template.max_uses
        )
        self.template = template
        self.skill_effects: List[SkillEffect] = []
        
        # 从模板创建效果
        for effect_config in template.effects:
            effect = EffectFactory.create_from_config(effect_config)
            if effect:
                self.skill_effects.append(effect)
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放技能"""
        if not self.owner:
            return False
        
        # 检查冷却时间
        if self.current_cooldown > 0:
            return False
        
        # 检查使用次数
        if self.max_uses > 0 and self.uses_remaining <= 0:
            return False
        
        # 检查TP
        current_tp = self.owner.get_attribute('current_tp')
        if current_tp < self.tp_cost:
            return False
        
        # 检查目标
        if self.template.target_type in [TargetType.SELECT, TargetType.SELECT_EXCEPT_SELF]:
            if not target:
                return False
            if self.template.target_type == TargetType.SELECT_EXCEPT_SELF and target == self.owner:
                return False
            if not target.is_alive():
                return False
        
        # 检查距离
        if (self.template.distance_check and target and target != self.owner and 
            hasattr(self.owner, 'room_obj') and self.owner.room_obj):
            distance = self._calculate_distance(self.owner, target)
            max_distance = self.owner.get_attribute('distance')
            if distance > max_distance:
                return False
        
        return True
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 获取目标列表
        targets = self._get_targets(target)
        if not targets:
            return False
        
        # 应用效果
        results = []
        for skill_target in targets:
            target_results = []
            for effect in self.skill_effects:
                if effect.can_apply(self.owner, skill_target, {}):
                    context = self._build_context(skill_target)
                    result = effect.apply(self.owner, skill_target, context)
                    target_results.append(result)
            results.append({'target': skill_target, 'effects': target_results})
        
        # 开始冷却
        self.start_cooldown()
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner, 
                  target=target,
                  data={'skill': self, 'results': results})
        
        return True
    
    def _get_targets(self, primary_target=None) -> List['BaseRole']:
        """获取目标列表"""
        if not hasattr(self.owner, 'room_obj') or not self.owner.room_obj:
            return []
        
        all_players = [p for p in self.owner.room_obj.player_list.values() if p.is_alive()]
        
        if self.template.target_type == TargetType.SELF:
            return [self.owner]
        elif self.template.target_type == TargetType.SELECT:
            return [primary_target] if primary_target else []
        elif self.template.target_type == TargetType.SELECT_EXCEPT_SELF:
            return [primary_target] if primary_target and primary_target != self.owner else []
        elif self.template.target_type == TargetType.ALL:
            return all_players
        elif self.template.target_type == TargetType.ALL_EXCEPT_SELF:
            return [p for p in all_players if p != self.owner]
        elif self.template.target_type == TargetType.NEAREST:
            return [self._find_nearest_target()]
        elif self.template.target_type == TargetType.RANDOM:
            import random
            others = [p for p in all_players if p != self.owner]
            return [random.choice(others)] if others else []
        
        return []
    
    def _find_nearest_target(self) -> Optional['BaseRole']:
        """找到最近的目标"""
        if not hasattr(self.owner, 'room_obj') or not self.owner.room_obj:
            return None
        
        all_players = [p for p in self.owner.room_obj.player_list.values() 
                      if p.is_alive() and p != self.owner]
        
        if not all_players:
            return None
        
        nearest = None
        min_distance = float('inf')
        
        for player in all_players:
            distance = self._calculate_distance(self.owner, player)
            if distance < min_distance:
                min_distance = distance
                nearest = player
        
        return nearest
    
    def _calculate_distance(self, player1: 'BaseRole', player2: 'BaseRole') -> int:
        """计算两个玩家之间的距离"""
        if not hasattr(self.owner, 'room_obj') or not self.owner.room_obj:
            return 0
        
        runway_length = len(self.owner.room_obj.runway)
        pos1, pos2 = player1.position, player2.position
        
        # 计算环形跑道上的最短距离
        direct_distance = abs(pos1 - pos2)
        wrap_distance = runway_length - direct_distance
        
        return min(direct_distance, wrap_distance)
    
    def _build_context(self, target: 'BaseRole') -> Dict[str, Any]:
        """构建技能上下文"""
        context = {
            'skill': self,
            'caster': self.owner,
            'target': target,
        }
        
        if hasattr(self.owner, 'room_obj') and self.owner.room_obj:
            context.update({
                'runway': self.owner.room_obj.runway,
                'all_players': list(self.owner.room_obj.player_list.values()),
                'room': self.owner.room_obj
            })
        
        return context


class SkillManager:
    """技能管理器"""
    
    def __init__(self):
        self.skill_templates: Dict[str, SkillTemplate] = {}
    
    def register_skill_template(self, template: SkillTemplate):
        """注册技能模板"""
        self.skill_templates[template.template_id] = template
    
    def create_skill(self, template_id: str) -> Optional[ConfigurableSkill]:
        """创建技能"""
        template = self.skill_templates.get(template_id)
        if template:
            return template.create_skill()
        return None
    
    def load_skills_from_config(self, config_path: str):
        """从配置文件加载技能"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            for skill_config in config.get('skills', []):
                template = self._create_template_from_config(skill_config)
                if template:
                    self.register_skill_template(template)
        except Exception as e:
            print(f"Failed to load skills from {config_path}: {e}")
    
    def _create_template_from_config(self, config: Dict[str, Any]) -> Optional[SkillTemplate]:
        """从配置创建技能模板"""
        try:
            target_type_str = config.get('target_type', 'select')
            target_type = TargetType(target_type_str)
            
            return SkillTemplate(
                template_id=config['id'],
                name=config['name'],
                description=config.get('description', ''),
                tp_cost=config.get('tp_cost', 0),
                target_type=target_type,
                effects=config.get('effects', []),
                cooldown=config.get('cooldown', 0),
                max_uses=config.get('max_uses', -1),
                distance_check=config.get('distance_check', True)
            )
        except Exception as e:
            print(f"Failed to create skill template from config: {e}")
            return None


# 全局技能管理器实例
skill_manager = SkillManager()


def register_skill_template(template: SkillTemplate):
    """注册技能模板的便利函数"""
    skill_manager.register_skill_template(template)


def create_skill(template_id: str) -> Optional[ConfigurableSkill]:
    """创建技能的便利函数"""
    return skill_manager.create_skill(template_id)


def load_skills_from_config(config_path: str):
    """从配置文件加载技能的便利函数"""
    skill_manager.load_skills_from_config(config_path)
