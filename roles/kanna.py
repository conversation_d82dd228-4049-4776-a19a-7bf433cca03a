# 环奈角色实现 - 新架构示例
from typing import Dict, Any, Optional
from core.base_role import BaseRole, RoleState
from core.components import SkillComponent, ComponentType
from core.effects import DamageEffect, AttributeChangeEffect, AOEEffect, EffectFactory
from core.events import EventType, GameEvent, fire_event
from core.role_factory import role_decorator


class KannaNormalAttack(SkillComponent):
    """环奈普通攻击"""
    
    def __init__(self):
        super().__init__(
            skill_id="kanna_normal_attack",
            name="普通攻击",
            description="对目标造成0(+1.0自身攻击力)伤害",
            tp_cost=0
        )
        self.damage_effect = DamageEffect(
            base_damage=0,
            scale_attr="attack",
            scale_ratio=1.0
        )
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放"""
        if not self.owner or not target:
            return False
        
        # 检查目标是否有效
        if target == self.owner or not target.is_alive():
            return False
        
        # 检查TP是否足够
        current_tp = self.owner.get_attribute('current_tp')
        return current_tp >= self.tp_cost
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 应用伤害效果
        context = {'skill': self, 'caster': self.owner}
        result = self.damage_effect.apply(self.owner, target, context)
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner, 
                  target=target,
                  data={'skill': self, 'result': result})
        
        return True


class KannaShockBlade(SkillComponent):
    """环奈冲击之刃"""
    
    def __init__(self):
        super().__init__(
            skill_id="kanna_shock_blade",
            name="冲击之刃",
            description="对目标造成及其半径3范围内所有玩家造成90(+1.2自身攻击力)伤害，并降低20护甲",
            tp_cost=20
        )
        
        # 创建伤害效果
        self.damage_effect = DamageEffect(
            base_damage=90,
            scale_attr="attack",
            scale_ratio=1.2
        )
        
        # 创建护甲降低效果
        self.armor_reduce_effect = AttributeChangeEffect(
            attr_name="defense",
            base_change=-20
        )
        
        # 创建AOE效果
        self.aoe_effect = AOEEffect(
            radius=3,
            include_self=False,
            effects=[self.damage_effect, self.armor_reduce_effect]
        )
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放"""
        if not self.owner or not target:
            return False
        
        if target == self.owner or not target.is_alive():
            return False
        
        current_tp = self.owner.get_attribute('current_tp')
        return current_tp >= self.tp_cost
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 应用AOE效果
        context = {
            'skill': self, 
            'caster': self.owner,
            'runway': getattr(self.owner, 'room_obj', None) and self.owner.room_obj.runway or [],
            'all_players': getattr(self.owner, 'room_obj', None) and list(self.owner.room_obj.player_list.values()) or []
        }
        result = self.aoe_effect.apply(self.owner, target, context)
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner, 
                  target=target,
                  data={'skill': self, 'result': result})
        
        return True


class KannaGiftFromHeaven(SkillComponent):
    """环奈天赐之物"""
    
    def __init__(self):
        super().__init__(
            skill_id="kanna_gift_from_heaven",
            name="天赐之物",
            description="所有玩家增加50点攻击力和10%暴击率，自身持续永久，其他玩家持续1个自我回合",
            tp_cost=30
        )
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放"""
        if not self.owner:
            return False
        
        current_tp = self.owner.get_attribute('current_tp')
        return current_tp >= self.tp_cost
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 获取所有玩家
        if not hasattr(self.owner, 'room_obj') or not self.owner.room_obj:
            return False
        
        all_players = list(self.owner.room_obj.player_list.values())
        
        # 对所有其他玩家应用临时buff
        for player in all_players:
            if player != self.owner and player.is_alive():
                # 这里应该添加临时buff，暂时直接修改属性
                player.change_attribute('attack', 50)
                player.change_attribute('crit_rate', 10)
        
        # 对自己应用永久buff
        self.owner.change_attribute('attack', 50)
        self.owner.change_attribute('crit_rate', 10)
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner,
                  data={'skill': self, 'targets_affected': len(all_players)})
        
        return True


@role_decorator(
    role_id=1701,
    name="环奈",
    position="输出型",
    health=1000,
    attack=110,
    defense=80,
    distance=9,
    tp=0,
    crit=10,
    description="桥本环奈，拥有强大的AOE技能和团队增益能力"
)
class KannaRole(BaseRole):
    """环奈角色类"""
    
    def get_role_position(self) -> str:
        return "输出型"
    
    def init_role_data(self, role_data: Dict[str, Any]):
        """初始化角色数据"""
        # 设置基础属性
        self.set_base_attribute('max_health', role_data.get('health', 1000))
        self.set_base_attribute('current_health', role_data.get('health', 1000))
        self.set_base_attribute('attack', role_data.get('attack', 110))
        self.set_base_attribute('defense', role_data.get('defense', 80))
        self.set_base_attribute('distance', role_data.get('distance', 9))
        self.set_base_attribute('current_tp', role_data.get('tp', 0))
        self.set_base_attribute('crit_rate', role_data.get('crit', 10))
        
        # 添加技能
        self.add_component(KannaNormalAttack())
        self.add_component(KannaShockBlade())
        self.add_component(KannaGiftFromHeaven())
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        super().handle_event(event)
        
        # 环奈特有的事件处理逻辑可以在这里添加
        if event.event_type == EventType.SKILL_CAST and event.source == self:
            # 技能释放后的特殊处理
            pass
