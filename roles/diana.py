# 嘉然角色实现 - 展示复杂角色的迁移
from typing import Dict, Any, Optional
from core.base_role import BaseRole, RoleState
from core.components import SkillComponent, PassiveComponent, ComponentType
from core.effects import DamageEffect, AttributeChangeEffect, EffectFactory
from core.events import EventType, GameEvent, fire_event
from core.role_factory import role_decorator
from core.buff_system import create_buff


class SweetiePassive(PassiveComponent):
    """嘉然的嘉心糖被动技能"""
    
    def __init__(self):
        super().__init__(
            passive_id="diana_sweetie_passive",
            name="嘉心糖支持",
            description="嘉然拥有嘉心糖的支持，每个嘉心糖提供属性加成"
        )
        self.sweetie_count = 3  # 初始嘉心糖数量
        self.max_sweetie = 10
    
    def apply_passive_effect(self):
        """应用被动效果"""
        if not self.owner:
            return
        
        # 根据嘉心糖数量计算属性加成
        self._update_sweetie_bonuses()
    
    def _update_sweetie_bonuses(self):
        """更新嘉心糖加成"""
        if not self.owner:
            return
        
        # 移除旧的修饰符
        for attr_name in ['max_health', 'attack', 'defense', 'crit_rate']:
            attr_component = self.owner.get_component(attr_name)
            if attr_component and hasattr(attr_component, 'remove_modifier'):
                attr_component.remove_modifier('sweetie_bonus')
        
        # 添加新的修饰符
        health_bonus = 150 * self.sweetie_count
        attack_bonus = 30 * self.sweetie_count
        defense_bonus = 30 * self.sweetie_count
        crit_bonus = 10 * self.sweetie_count
        
        # 应用加成
        self.owner.get_component('max_health').add_modifier('add', health_bonus, 'sweetie_bonus')
        self.owner.get_component('attack').add_modifier('add', attack_bonus, 'sweetie_bonus')
        self.owner.get_component('defense').add_modifier('add', defense_bonus, 'sweetie_bonus')
        self.owner.get_component('crit_rate').add_modifier('add', crit_bonus, 'sweetie_bonus')
        
        # 更新当前生命值
        current_health = self.owner.get_attribute('current_health')
        max_health = self.owner.get_attribute('max_health')
        if current_health > max_health:
            self.owner.set_base_attribute('current_health', max_health)
    
    def add_sweetie(self, count: int):
        """增加嘉心糖"""
        old_count = self.sweetie_count
        self.sweetie_count = min(self.sweetie_count + count, self.max_sweetie)
        if self.sweetie_count != old_count:
            self._update_sweetie_bonuses()
    
    def remove_sweetie(self, count: int):
        """减少嘉心糖"""
        old_count = self.sweetie_count
        self.sweetie_count = max(0, self.sweetie_count - count)
        if self.sweetie_count != old_count:
            self._update_sweetie_bonuses()
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        if not self.enabled or not self.owner:
            return
        
        # 当生命值变化时，检查是否需要调整嘉心糖数量
        if (event.event_type == EventType.ATTR_CHANGED and 
            event.source == self.owner and 
            event.data.get('attribute') == 'current_health'):
            
            self._check_sweetie_loss()
    
    def _check_sweetie_loss(self):
        """检查嘉心糖损失"""
        if not self.owner:
            return
        
        current_health = self.owner.get_attribute('current_health')
        base_health = 500  # 基础生命值
        
        # 计算应有的嘉心糖数量
        health_above_base = max(0, current_health - base_health)
        expected_sweetie = min(self.max_sweetie, health_above_base // 150)
        
        if expected_sweetie < self.sweetie_count:
            self.sweetie_count = expected_sweetie
            self._update_sweetie_bonuses()


class DianaNormalAttack(SkillComponent):
    """嘉然普通攻击"""
    
    def __init__(self):
        super().__init__(
            skill_id="diana_normal_attack",
            name="普通攻击",
            description="对目标造成伤害，每有一个嘉心糖，此伤害提升8%；并将伤害的15%转化为生命值",
            tp_cost=10
        )
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放"""
        if not self.owner or not target:
            return False
        
        if not target.is_alive():
            return False
        
        current_tp = self.owner.get_attribute('current_tp')
        return current_tp >= self.tp_cost
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 计算伤害
        base_damage = self.owner.get_attribute('attack')
        
        # 获取嘉心糖加成
        sweetie_passive = self.owner.get_component('diana_sweetie_passive')
        sweetie_count = sweetie_passive.sweetie_count if sweetie_passive else 0
        damage_multiplier = 1.0 + (sweetie_count * 0.08)
        
        final_damage = base_damage * damage_multiplier
        
        # 应用伤害
        target.change_attribute('current_health', -final_damage)
        
        # 生命偷取
        heal_amount = final_damage * 0.15
        self.owner.change_attribute('current_health', heal_amount)
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner, 
                  target=target,
                  data={'skill': self, 'damage': final_damage, 'heal': heal_amount})
        
        return True


class DianaStartStream(SkillComponent):
    """嘉然开播技能"""
    
    def __init__(self):
        super().__init__(
            skill_id="diana_start_stream",
            name="开播",
            description="嘉然立即获得1个嘉心糖，并在接下来的2个自我回合内每回合获得2个嘉心糖",
            tp_cost=25
        )
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放"""
        if not self.owner:
            return False
        
        current_tp = self.owner.get_attribute('current_tp')
        return current_tp >= self.tp_cost
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 立即获得1个嘉心糖
        sweetie_passive = self.owner.get_component('diana_sweetie_passive')
        if sweetie_passive:
            sweetie_passive.add_sweetie(1)
        
        # 添加持续获得嘉心糖的buff
        streaming_buff = create_buff('diana_streaming')
        if streaming_buff:
            self.owner.add_component(streaming_buff)
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner,
                  data={'skill': self})
        
        return True


class DianaCatPoison(SkillComponent):
    """嘉然顿顿解馋技能"""
    
    def __init__(self):
        super().__init__(
            skill_id="diana_cat_poison",
            name="顿顿解馋",
            description="对目标造成伤害并使其获得猫中毒状态",
            tp_cost=30
        )
    
    def can_cast(self, target=None) -> bool:
        """检查是否可以释放"""
        if not self.owner or not target:
            return False
        
        if target == self.owner or not target.is_alive():
            return False
        
        current_tp = self.owner.get_attribute('current_tp')
        return current_tp >= self.tp_cost
    
    def cast(self, target=None) -> bool:
        """释放技能"""
        if not self.can_cast(target):
            return False
        
        # 消耗TP
        self.owner.change_attribute('current_tp', -self.tp_cost)
        
        # 计算伤害
        base_damage = 100
        attack_damage = self.owner.get_attribute('attack') * 1.5
        total_damage = base_damage + attack_damage
        
        # 应用伤害
        target.change_attribute('current_health', -total_damage)
        
        # 添加猫中毒状态
        cat_poison_buff = create_buff('cat_poison')
        if cat_poison_buff:
            target.add_component(cat_poison_buff)
        
        # 触发技能释放事件
        fire_event(EventType.SKILL_CAST, 
                  source=self.owner, 
                  target=target,
                  data={'skill': self, 'damage': total_damage})
        
        return True


@role_decorator(
    role_id=5105,
    name="嘉然",
    position="特殊型",
    health=500,
    attack=10,
    defense=10,
    distance=6,
    tp=0,
    crit=0,
    description="嘉然Diana，拥有独特的嘉心糖机制"
)
class DianaRole(BaseRole):
    """嘉然角色类"""
    
    def get_role_position(self) -> str:
        return "特殊型"
    
    def init_role_data(self, role_data: Dict[str, Any]):
        """初始化角色数据"""
        # 设置基础属性
        self.set_base_attribute('max_health', role_data.get('health', 500))
        self.set_base_attribute('current_health', role_data.get('health', 500))
        self.set_base_attribute('attack', role_data.get('attack', 10))
        self.set_base_attribute('defense', role_data.get('defense', 10))
        self.set_base_attribute('distance', role_data.get('distance', 6))
        self.set_base_attribute('current_tp', role_data.get('tp', 0))
        self.set_base_attribute('crit_rate', role_data.get('crit', 0))
        
        # 添加被动技能
        sweetie_passive = SweetiePassive()
        self.add_component(sweetie_passive)
        sweetie_passive.apply_passive_effect()
        
        # 添加技能
        self.add_component(DianaNormalAttack())
        self.add_component(DianaStartStream())
        self.add_component(DianaCatPoison())
    
    def handle_event(self, event: GameEvent):
        """处理事件"""
        super().handle_event(event)
        
        # 嘉然特有的事件处理逻辑
        if (event.event_type == EventType.AFTER_HURT and 
            event.target != self and 
            hasattr(event.target, 'has_component') and
            event.target.has_component('cat_poison')):
            
            # 猫中毒目标受到伤害时，嘉然获得嘉心糖和TP
            sweetie_passive = self.get_component('diana_sweetie_passive')
            if sweetie_passive:
                sweetie_passive.add_sweetie(3)
            
            self.change_attribute('current_tp', 10)
            event.target.change_attribute('current_tp', -10)
